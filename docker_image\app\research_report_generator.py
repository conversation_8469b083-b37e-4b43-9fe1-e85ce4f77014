#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能研报生成系统
基于多Agent协同的公司个股研报自动生成系统
集成RAG技术、工具调用、多模态生成等前沿技术
"""

import pandas as pd
import numpy as np
import json
import os
import logging
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
import requests
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.table import WD_TABLE_ALIGNMENT
import matplotlib.font_manager as fm
from config import config
import warnings
warnings.filterwarnings('ignore')



# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('research_report.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)



@dataclass
class AgentMessage:
    """Agent间通信消息格式"""
    sender: str
    receiver: str
    message_type: str
    content: Any
    timestamp: datetime
    
@dataclass
class AnalysisResult:
    """分析结果数据结构"""
    section: str
    content: str
    charts: List[str]
    data_sources: List[str]
    confidence: float

@dataclass
class CompanyInfo:
    """公司基本信息"""
    stock_code: str
    company_name: str
    industry: str
    market: str
    analysis_date: str

class BaseAgent:
    """Agent基类"""
    
    def __init__(self, name: str, llm_config: dict = None):
        self.name = name
        self.llm_config = llm_config or config.llm
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Bearer {self.llm_config.api_key}',
            'Content-Type': 'application/json'
        })
        self.message_history = []
        
    def send_message(self, receiver: str, message_type: str, content: Any) -> AgentMessage:
        """发送消息给其他Agent"""
        message = AgentMessage(
            sender=self.name,
            receiver=receiver,
            message_type=message_type,
            content=content,
            timestamp=datetime.now()
        )
        self.message_history.append(message)
        logger.info(f"{self.name} -> {receiver}: {message_type}")
        return message
    
    def call_llm(self, prompt: str, system_prompt: str = None) -> str:
        """调用LLM API"""
        try:
            messages = []
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            messages.append({"role": "user", "content": prompt})
            
            payload = {
                "model": self.llm_config.model,
                "messages": messages,
                "max_tokens": self.llm_config.max_tokens,
                "temperature": self.llm_config.temperature
            }
            
            response = self.session.post(
                f"{self.llm_config.base_url}/chat/completions",
                json=payload,
                timeout=60
            )
            response.raise_for_status()
            
            result = response.json()
            return result['choices'][0]['message']['content']
            
        except Exception as e:
            logger.error(f"LLM调用失败: {e}")
            return f"分析失败: {str(e)}"

class DataAnalysisAgent(BaseAgent):
    """数据分析Agent - 负责财务数据分析和指标计算"""

    def __init__(self):
        super().__init__("DataAnalysisAgent")
        self.financial_data = {}
        self.calculated_metrics = {}
        self.existing_report = ""

    def load_financial_data(self) -> Dict[str, pd.DataFrame]:
        """加载财务数据"""
        try:
            data_files = {
                'main_indicators': '主要指标.csv',
                'income_statement': '利润表.csv',
                'balance_sheet': '资产负债表.csv',
                'cash_flow': '现金流量表.csv',
                'valuation_comparison': '估值对比.csv',
                'growth_comparison': '成长性对比.csv',
                'scale_comparison': '规模对比.csv'
            }

            for key, filename in data_files.items():
                if os.path.exists(filename):
                    df = pd.read_csv(filename, encoding='utf-8-sig')
                    self.financial_data[key] = df
                    logger.info(f"加载数据文件: {filename}, 形状: {df.shape}")
                else:
                    logger.warning(f"数据文件不存在: {filename}")

            # 加载现有的财务分析报告
            self._load_existing_report()

            return self.financial_data

        except Exception as e:
            logger.error(f"加载财务数据失败: {e}")
            return {}

    def _load_existing_report(self):
        """加载现有的财务分析报告"""
        try:
            report_files = [f for f in os.listdir('analysis_reports') if f.endswith('.md')]
            if report_files:
                report_path = os.path.join('analysis_reports', report_files[0])
                with open(report_path, 'r', encoding='utf-8') as f:
                    self.existing_report = f.read()
                logger.info(f"加载现有财务分析报告: {report_path}")
            else:
                logger.warning("未找到现有财务分析报告")
        except Exception as e:
            logger.error(f"加载现有财务分析报告失败: {e}")
    
    def calculate_financial_ratios(self) -> Dict[str, Any]:
        """计算关键财务比率"""
        try:
            ratios = {}
            
            # 从主要指标中提取数据
            if 'main_indicators' in self.financial_data:
                df = self.financial_data['main_indicators']
                
                # 盈利能力指标
                ratios['profitability'] = {
                    'roe': self._extract_metric(df, 'ROE'),
                    'roa': self._extract_metric(df, 'ROA'),
                    'gross_margin': self._extract_metric(df, '毛利率'),
                    'net_margin': self._extract_metric(df, '净利率')
                }
                
                # 成长性指标
                ratios['growth'] = {
                    'revenue_growth': self._extract_metric(df, '营业收入同比增长率'),
                    'profit_growth': self._extract_metric(df, '净利润同比增长率'),
                    'eps_growth': self._extract_metric(df, 'EPS同比增长率')
                }
                
                # 估值指标
                ratios['valuation'] = {
                    'pe_ratio': self._extract_metric(df, '市盈率TTM'),
                    'pb_ratio': self._extract_metric(df, '市净率'),
                    'ps_ratio': self._extract_metric(df, '市销率')
                }
                
            self.calculated_metrics = ratios
            logger.info("财务比率计算完成")
            return ratios
            
        except Exception as e:
            logger.error(f"计算财务比率失败: {e}")
            return {}
    
    def _extract_metric(self, df: pd.DataFrame, metric_name: str) -> List[float]:
        """从数据框中提取指标数据"""
        try:
            if metric_name in df.columns:
                values = df[metric_name].dropna().tolist()
                return [float(v) for v in values if pd.notna(v)]
            return []
        except:
            return []
    
    def analyze_financial_trends(self) -> AnalysisResult:
        """基于现有财务分析报告进行深度分析"""
        system_prompt = """你是一位资深的证券分析师，请基于现有的财务分析报告进行深度解读和专业总结。
        保持原有分析的准确性，同时提供更深入的投资洞察。"""

        prompt = f"""
        请基于以下现有的财务分析报告，撰写专业的财务分析章节：

        现有财务分析报告：
        {self.existing_report}

        请从以下维度进行深度分析和总结：
        1. 财务表现核心亮点总结
        2. 盈利能力深度分析（结合行业背景）
        3. 成长性质量评估（可持续性分析）
        4. 财务风险识别与评估
        5. 与同行业公司对比分析
        6. 财务数据对投资价值的指示意义

        要求：
        - 保持数据的准确性
        - 提供专业的投资洞察
        - 突出关键财务指标变化的意义
        - 评估财务趋势的可持续性
        - 识别潜在的财务风险和机会
        """

        content = self.call_llm(prompt, system_prompt)

        return AnalysisResult(
            section="财务分析",
            content=content,
            charts=self._get_available_charts(),
            data_sources=["analysis_reports/财务分析报告.md", "主要指标.csv", "利润表.csv"],
            confidence=0.95
        )

    def _get_available_charts(self) -> List[str]:
        """获取可用的图表文件"""
        chart_files = []
        if os.path.exists('report_images'):
            for file in os.listdir('report_images'):
                if file.endswith('.png'):
                    chart_files.append(os.path.join('report_images', file))
        return chart_files
    
    def _summarize_data(self) -> Dict[str, Any]:
        """汇总数据概况"""
        summary = {}
        for key, df in self.financial_data.items():
            summary[key] = {
                'shape': df.shape,
                'columns': df.columns.tolist()[:5],  # 只显示前5列
                'date_range': self._get_date_range(df)
            }
        return summary
    
    def _get_date_range(self, df: pd.DataFrame) -> str:
        """获取数据时间范围"""
        date_columns = [col for col in df.columns if '年' in col or '季' in col or 'date' in col.lower()]
        if date_columns:
            return f"包含{len(date_columns)}个时间期间"
        return "时间范围未知"

class MarketViewpointAgent(BaseAgent):
    """市场观点分析Agent - 负责分析股吧观点作为研报分析内容"""

    def __init__(self):
        super().__init__("MarketViewpointAgent")
        self.viewpoint_data = {}

    def load_viewpoint_data(self) -> Dict[str, Any]:
        """加载市场观点数据"""
        try:
            if os.path.exists('guba_viewpoint_analysis.json'):
                with open('guba_viewpoint_analysis.json', 'r', encoding='utf-8') as f:
                    self.viewpoint_data = json.load(f)
                logger.info(f"加载观点数据: {len(self.viewpoint_data.get('analyses', []))}条")
            else:
                logger.warning("观点分析文件不存在")
            return self.viewpoint_data
        except Exception as e:
            logger.error(f"加载观点数据失败: {e}")
            return {}

    def analyze_market_viewpoints(self) -> AnalysisResult:
        """分析市场观点作为研报内容"""
        system_prompt = """你是一位资深的证券分析师，请将股吧观点数据整合到专业研报分析中。
        重点关注观点中的投资逻辑、业务分析和市场判断，并正确引用来源。"""

        # 提取关键观点
        analyses = self.viewpoint_data.get('analyses', [])
        key_viewpoints = self._extract_key_viewpoints(analyses)

        prompt = f"""
        请基于以下股吧观点数据撰写专业的市场观点分析章节：

        数据概况：
        - 总观点数：{len(analyses)}
        - 分析时间：{self.viewpoint_data.get('metadata', {}).get('analysis_time', '未知')}

        关键观点摘要：
        {key_viewpoints}

        请从以下角度进行专业分析：
        1. 市场对公司基本面的主要观点
        2. 投资者关注的核心业务发展
        3. 对公司战略转型的市场看法
        4. 估值和投资价值的市场判断
        5. 主要风险因素的市场认知

        要求：
        - 将观点整合为专业分析内容
        - 正确引用具体观点来源
        - 客观评估观点的合理性
        - 避免简单的情绪分析
        - 突出对投资决策有价值的信息
        """

        content = self.call_llm(prompt, system_prompt)

        return AnalysisResult(
            section="市场观点分析",
            content=content,
            charts=[],
            data_sources=["guba_viewpoint_analysis.json"],
            confidence=0.8
        )

    def _extract_key_viewpoints(self, analyses: List[Dict]) -> str:
        """提取关键观点内容"""
        key_points = []

        for i, analysis in enumerate(analyses[:10]):  # 取前10个重要观点
            source = analysis.get('source', {})
            title = source.get('title', '')
            author = source.get('author', '未知')
            publish_time = source.get('publish_time', '')
            summary = analysis.get('一句话总结', '')
            main_points = analysis.get('主要论点', [])

            if title and summary:
                viewpoint = f"""
观点{i+1}：{title}
来源：{author}，发布时间：{publish_time}
核心观点：{summary}
主要论点：{'; '.join(main_points[:3]) if main_points else '无'}
"""
                key_points.append(viewpoint)

        return '\n'.join(key_points)

    def get_viewpoint_references(self) -> List[str]:
        """获取观点引用列表"""
        references = []
        analyses = self.viewpoint_data.get('analyses', [])

        for analysis in analyses:
            source = analysis.get('source', {})
            title = source.get('title', '')
            author = source.get('author', '')
            url = source.get('url', '')
            publish_time = source.get('publish_time', '')

            if title and author:
                ref = f"{author}. {title}. {publish_time}. {url}"
                references.append(ref)

        return references

class ChartGenerationAgent(BaseAgent):
    """图表生成Agent - 负责管理和使用现有图表"""

    def __init__(self):
        super().__init__("ChartGenerationAgent")
        self.available_charts = []
        self.chart_descriptions = {}
        self.chart_descriptions_md = {}
        self.analysis_reports = {}  # 存储分析报告内容

    def load_existing_charts(self) -> List[str]:
        """加载现有图表文件"""
        try:
            if os.path.exists('report_images'):
                chart_files = [f for f in os.listdir('report_images') if f.endswith('.png')]
                self.available_charts = [os.path.join('report_images', f) for f in chart_files]

                # 加载图表描述文档
                self._load_chart_descriptions()

                # 加载分析报告文档
                self._load_analysis_reports()

                logger.info(f"加载现有图表: {len(self.available_charts)}个")
                return self.available_charts
            else:
                logger.warning("report_images目录不存在")
                return []
        except Exception as e:
            logger.error(f"加载现有图表失败: {e}")
            return []

    def _load_chart_descriptions(self):
        """从markdown文件加载图表描述"""
        try:
            if os.path.exists('chart_descriptions.md'):
                with open('chart_descriptions.md', 'r', encoding='utf-8') as f:
                    content = f.read()

                # 解析markdown内容
                sections = content.split('## ')[1:]  # 跳过第一个空部分

                for section in sections:
                    lines = section.strip().split('\n')
                    if lines:
                        chart_name = lines[0].strip()

                        # 查找描述内容
                        description = ""
                        for line in lines[1:]:
                            if line.startswith('**描述**：'):
                                description = line.replace('**描述**：', '').strip()
                                break

                        if not description:
                            # 如果没有LLM生成的描述，使用默认描述
                            description = self._get_default_description(chart_name)

                        self.chart_descriptions_md[chart_name] = description

                logger.info(f"加载图表描述: {len(self.chart_descriptions_md)}个")
            else:
                logger.warning("chart_descriptions.md文件不存在，使用默认描述")
                self._use_default_descriptions()

        except Exception as e:
            logger.error(f"加载图表描述失败: {e}")
            self._use_default_descriptions()

    def _load_analysis_reports(self):
        """加载analysis_reports文件夹下的markdown分析报告"""
        try:
            if os.path.exists('analysis_reports'):
                report_files = [f for f in os.listdir('analysis_reports') if f.endswith('.md')]

                for report_file in report_files:
                    report_path = os.path.join('analysis_reports', report_file)
                    with open(report_path, 'r', encoding='utf-8') as f:
                        content = f.read()

                    # 根据文件名确定报告类型
                    if '财务分析' in report_file:
                        self.analysis_reports['financial_analysis'] = content
                    elif '行业分析' in report_file:
                        self.analysis_reports['industry_analysis'] = content
                    elif '技术分析' in report_file:
                        self.analysis_reports['technical_analysis'] = content
                    else:
                        # 通用分析报告
                        report_key = report_file.replace('.md', '').replace('报告', '').replace('_', '')
                        self.analysis_reports[report_key] = content

                logger.info(f"加载分析报告: {len(self.analysis_reports)}个")
            else:
                logger.warning("analysis_reports目录不存在")

        except Exception as e:
            logger.error(f"加载分析报告失败: {e}")

    def _get_default_description(self, chart_name: str) -> str:
        """获取默认图表描述"""
        default_descriptions = {
            '01_财务概览仪表板': '财务概览仪表板 - 展示公司主要财务指标概况，包括营收、利润、ROE、毛利率等核心指标',
            '02_估值对比_市盈率TTM': '市盈率TTM对比 - 与同行业公司估值水平对比，评估公司估值合理性',
            '02_成长性对比_EPS同比增长率': 'EPS同比增长率对比 - 盈利成长性行业对比，展示公司盈利增长能力',
            '02_成长性对比_营业收入同比增长率': '营业收入同比增长率对比 - 收入成长性行业对比，反映公司业务扩张能力',
            '02_规模对比_营业收入': '营业收入规模对比 - 与同行业公司规模对比，展示公司市场地位',
            '03_营收利润趋势': '营收利润趋势图 - 历史营业收入和利润变化趋势，反映公司经营发展轨迹',
            '04_盈利能力趋势': '盈利能力趋势图 - ROE、毛利率等盈利指标变化，评估盈利质量和可持续性',
            '05_利润表结构分析': '利润表结构分析 - 收入成本结构变化分析，揭示盈利结构优化情况',
            '06_现金流分析': '现金流分析图 - 经营、投资、筹资现金流分析，评估现金流质量',
            '07_估值分析': '估值分析图 - PE、PB等估值指标历史变化，判断估值水平合理性',
            '08_综合评分雷达图': '综合评分雷达图 - 多维度财务表现评分，全面评估公司财务健康度'
        }
        return default_descriptions.get(chart_name, f"图表: {chart_name}")

    def _use_default_descriptions(self):
        """使用默认描述"""
        for chart_path in self.available_charts:
            chart_name = os.path.basename(chart_path).replace('.png', '')
            self.chart_descriptions_md[chart_name] = self._get_default_description(chart_name)

    def get_chart_by_category(self, category: str) -> List[str]:
        """根据类别获取图表"""
        category_mapping = {
            'overview': ['01_财务概览仪表板.png'],
            'trends': ['03_营收利润趋势.png', '04_盈利能力趋势.png'],
            'comparison': ['02_估值对比_市盈率TTM.png', '02_成长性对比_EPS同比增长率.png',
                          '02_成长性对比_营业收入同比增长率.png', '02_规模对比_营业收入.png'],
            'analysis': ['05_利润表结构分析.png', '06_现金流分析.png', '07_估值分析.png'],
            'summary': ['08_综合评分雷达图.png']
        }

        charts = []
        for chart_name in category_mapping.get(category, []):
            chart_path = os.path.join('report_images', chart_name)
            if os.path.exists(chart_path):
                charts.append(chart_path)

        return charts

    def get_chart_description(self, chart_path: str) -> str:
        """获取图表描述"""
        chart_name = os.path.basename(chart_path)
        return self.chart_descriptions.get(chart_name, f"图表: {chart_name}")

    def get_all_charts_with_descriptions(self) -> Dict[str, str]:
        """获取所有图表及其描述"""
        result = {}
        for chart_path in self.available_charts:
            if os.path.exists(chart_path):
                description = self.get_chart_description(chart_path)
                result[chart_path] = description
        return result

    def select_charts_for_content(self, content_type: str, content_text: str) -> List[str]:
        """基于内容和分析报告智能选择合适的图表"""
        system_prompt = """你是一位专业的研报编辑，擅长为不同类型的分析内容选择最合适的图表。
        请根据内容类型、具体文字和相关分析报告，从可用图表中选择最相关、最能支撑分析观点的图表。"""

        # 准备图表信息
        chart_info = []
        for chart_path in self.available_charts:
            chart_name = os.path.basename(chart_path).replace('.png', '')
            description = self.chart_descriptions_md.get(chart_name, self._get_default_description(chart_name))
            chart_info.append(f"- {chart_name}: {description}")

        # 获取相关的分析报告内容
        relevant_analysis = self._get_relevant_analysis_content(content_type)

        prompt = f"""
        请为以下研报内容选择最合适的图表：

        内容类型：{content_type}
        内容摘要：{content_text[:500]}...

        相关分析报告参考：
        {relevant_analysis}

        可用图表：
        {chr(10).join(chart_info)}

        请基于以下原则选择2-4个最相关的图表：
        1. 图表内容与分析文字高度相关
        2. 能够有效支撑分析观点和结论
        3. 与分析报告中提到的关键指标和趋势匹配
        4. 图文配合提升信息传达效果
        5. 避免重复或无关的图表

        特别注意：
        - 如果内容涉及盈利能力，优先选择盈利能力相关图表
        - 如果内容涉及成长性，优先选择营收利润趋势和成长性对比图表
        - 如果内容涉及财务结构，优先选择利润表结构和现金流分析图表
        - 如果内容涉及估值，优先选择估值分析和行业对比图表

        请只返回选中的图表名称（不含.png后缀），每行一个，格式如：
        01_财务概览仪表板
        03_营收利润趋势
        """

        try:
            response = self.call_llm(prompt, system_prompt)

            # 解析响应，提取图表名称
            selected_charts = []
            for line in response.split('\n'):
                line = line.strip()
                if line and not line.startswith('请') and not line.startswith('根据') and not line.startswith('特别'):
                    # 清理可能的前缀符号
                    chart_name = line.replace('-', '').replace('*', '').replace('•', '').strip()

                    # 查找对应的图表路径
                    for chart_path in self.available_charts:
                        if chart_name in os.path.basename(chart_path):
                            selected_charts.append(chart_path)
                            break

            logger.info(f"为{content_type}选择了{len(selected_charts)}个图表")
            return selected_charts[:4]  # 最多返回4个图表

        except Exception as e:
            logger.error(f"智能图表选择失败: {e}")
            # 返回默认图表
            return self.get_chart_by_category('overview')[:2]

    def _get_relevant_analysis_content(self, content_type: str) -> str:
        """获取与内容类型相关的分析报告内容"""
        relevant_content = []

        # 根据内容类型匹配相关的分析报告
        if content_type in ["财务分析", "financial_analysis"]:
            if 'financial_analysis' in self.analysis_reports:
                content = self.analysis_reports['financial_analysis']
                # 提取关键部分
                relevant_sections = self._extract_relevant_sections(content, [
                    "盈利能力", "成长性", "财务健康", "利润率", "ROE", "ROA", "营业收入", "净利润"
                ])
                relevant_content.append(f"财务分析报告关键内容：\n{relevant_sections}")

        elif content_type in ["财务预测", "financial_forecast"]:
            if 'financial_analysis' in self.analysis_reports:
                content = self.analysis_reports['financial_analysis']
                # 提取趋势和预测相关内容
                relevant_sections = self._extract_relevant_sections(content, [
                    "趋势", "增长", "预测", "未来", "展望", "同比", "环比"
                ])
                relevant_content.append(f"财务趋势分析：\n{relevant_sections}")

        elif content_type in ["市场观点分析", "market_viewpoint_analysis"]:
            # 对于市场观点，主要参考财务分析中的关键指标
            if 'financial_analysis' in self.analysis_reports:
                content = self.analysis_reports['financial_analysis']
                relevant_sections = self._extract_relevant_sections(content, [
                    "关键发现", "评估", "风险", "机会", "竞争", "市场"
                ])
                relevant_content.append(f"财务分析关键发现：\n{relevant_sections}")

        # 如果没有找到特定内容，返回通用的财务概览
        if not relevant_content and 'financial_analysis' in self.analysis_reports:
            content = self.analysis_reports['financial_analysis'][:800]  # 取前800字符
            relevant_content.append(f"财务分析概览：\n{content}")

        return '\n\n'.join(relevant_content) if relevant_content else "暂无相关分析报告参考"

    def _extract_relevant_sections(self, content: str, keywords: List[str]) -> str:
        """从分析报告中提取包含关键词的相关段落"""
        lines = content.split('\n')
        relevant_lines = []

        for line in lines:
            # 检查是否包含关键词
            if any(keyword in line for keyword in keywords):
                relevant_lines.append(line)
            # 如果是表格或数据行，也包含进来
            elif '|' in line or any(char.isdigit() for char in line):
                if any(keyword in line for keyword in keywords):
                    relevant_lines.append(line)

        # 限制长度，避免prompt过长
        result = '\n'.join(relevant_lines[:20])  # 最多20行
        return result if result else content[:500]  # 如果没有匹配，返回前500字符
class FinancialForecastAgent(BaseAgent):
    """财务预测Agent - 负责生成财务预测分析"""

    def __init__(self):
        super().__init__("FinancialForecastAgent")

    def generate_financial_forecast(self, financial_data: Dict[str, pd.DataFrame],
                                  existing_report: str) -> AnalysisResult:
        """生成财务预测分析"""
        system_prompt = """你是一位资深的证券分析师和财务建模专家，擅长基于历史财务数据进行前瞻性预测。
        请基于历史数据和行业趋势，提供专业的财务预测分析。"""

        # 提取关键历史数据
        historical_summary = self._extract_historical_trends(financial_data, existing_report)

        prompt = f"""
        请基于以下历史财务数据和分析，生成专业的财务预测章节：

        历史财务数据摘要：
        {historical_summary}

        现有财务分析报告要点：
        {existing_report[:1000]}...

        请从以下维度进行财务预测：

        1. 营业收入预测（未来2-3年）
           - 基于历史增长趋势
           - 考虑行业发展前景
           - 主要业务驱动因素分析
           - 收入预测的关键假设

        2. 盈利能力预测
           - 毛利率变化趋势预测
           - 净利率改善路径分析
           - ROE恢复时间预测
           - 盈利拐点判断

        3. 关键财务指标预测
           - EPS预测及增长率
           - 现金流改善预期
           - 资产负债结构优化
           - 偿债能力变化趋势

        4. 估值预测分析
           - 合理估值区间预测
           - PE/PB估值修复空间
           - 目标价格测算
           - 估值催化因素识别

        5. 预测风险提示
           - 预测假设的敏感性分析
           - 主要不确定性因素
           - 情景分析（乐观/中性/悲观）
           - 预测调整的触发条件

        要求：
        - 预测要有合理的逻辑支撑
        - 明确关键假设和前提条件
        - 提供量化的预测数据
        - 充分考虑风险因素
        - 符合专业分析标准
        """

        content = self.call_llm(prompt, system_prompt)

        return AnalysisResult(
            section="财务预测",
            content=content,
            charts=[],
            data_sources=["历史财务数据", "行业分析", "管理层指引"],
            confidence=0.7
        )

    def _extract_historical_trends(self, financial_data: Dict[str, pd.DataFrame],
                                 existing_report: str) -> str:
        """提取历史趋势数据"""
        trends = []

        # 从主要指标中提取趋势
        if 'main_indicators' in financial_data:
            df = financial_data['main_indicators']

            # 营收趋势
            if '营业收入' in df.columns:
                revenue_data = df['营业收入'].dropna().tolist()
                if len(revenue_data) >= 3:
                    recent_growth = ((revenue_data[-1] / revenue_data[-2]) - 1) * 100
                    trends.append(f"营业收入最近增长率: {recent_growth:.1f}%")

            # 净利润趋势
            if '净利润' in df.columns:
                profit_data = df['净利润'].dropna().tolist()
                if len(profit_data) >= 2:
                    profit_trend = "改善" if profit_data[-1] > profit_data[-2] else "恶化"
                    trends.append(f"净利润趋势: {profit_trend}")

        # 从现有报告中提取关键信息
        if "2024年" in existing_report:
            trends.append("2024年数据已公布，可作为预测基准")

        return "; ".join(trends) if trends else "历史数据有限"

class ContentWritingAgent(BaseAgent):
    """内容撰写Agent - 负责撰写研报各个章节"""

    def __init__(self):
        super().__init__("ContentWritingAgent")

    def write_executive_summary(self, company_info: CompanyInfo, analysis_results: List[AnalysisResult]) -> str:
        """撰写投资要点/核心观点"""
        system_prompt = """你是一位资深的证券分析师，擅长撰写专业的投资研究报告。
        请基于分析结果撰写简洁有力的投资要点，突出核心观点和投资逻辑。"""

        # 整合分析结果
        key_findings = []
        for result in analysis_results:
            key_findings.append(f"{result.section}: {result.content[:200]}...")

        prompt = f"""
        请为{company_info.company_name}({company_info.stock_code})撰写投资要点，基于以下分析结果：

        公司基本信息：
        - 股票代码：{company_info.stock_code}
        - 公司名称：{company_info.company_name}
        - 所属行业：{company_info.industry}
        - 分析日期：{company_info.analysis_date}

        关键分析发现：
        {chr(10).join(key_findings)}

        请撰写3-5个核心投资要点，每个要点包含：
        1. 明确的观点表述
        2. 支撑数据或事实
        3. 投资逻辑说明

        要求：
        - 语言专业、简洁有力
        - 突出投资价值和风险
        - 符合证券研报写作规范
        """

        return self.call_llm(prompt, system_prompt)

    def write_company_overview(self, company_info: CompanyInfo) -> str:
        """撰写公司概况"""
        system_prompt = """你是一位专业的行业分析师，请撰写客观、全面的公司概况介绍。"""

        prompt = f"""
        请为{company_info.company_name}撰写公司概况，包含以下内容：

        基本信息：
        - 股票代码：{company_info.stock_code}
        - 公司全称：{company_info.company_name}
        - 所属行业：{company_info.industry}
        - 上市市场：{company_info.market}

        请从以下维度撰写：
        1. 公司主营业务介绍
        2. 核心产品和服务
        3. 行业地位和市场份额
        4. 核心竞争优势
        5. 发展战略和规划

        要求：
        - 内容客观、准确
        - 突出公司特色和优势
        - 语言专业、流畅
        - 篇幅适中（300-500字）
        """

        return self.call_llm(prompt, system_prompt)

    def write_financial_analysis(self, analysis_result: AnalysisResult) -> str:
        """撰写财务分析章节"""
        system_prompt = """你是一位CFA持证人，擅长深度财务分析。请基于数据进行专业、深入的财务分析。"""

        prompt = f"""
        请基于以下财务分析结果撰写详细的财务分析章节：

        {analysis_result.content}

        请从以下维度深入分析：
        1. 盈利能力分析
           - ROE、ROA变化趋势及驱动因素
           - 毛利率、净利率水平及变化原因
           - 与行业平均水平对比

        2. 成长性分析
           - 营业收入增长质量分析
           - 净利润增长可持续性
           - 主要业务增长驱动力

        3. 财务质量评估
           - 现金流与利润匹配度
           - 资产质量分析
           - 负债结构合理性

        4. 估值水平分析
           - PE、PB、PS等估值指标
           - 历史估值区间分析
           - 相对估值合理性

        要求：
        - 分析深入、逻辑清晰
        - 数据支撑充分
        - 结论明确、有说服力
        - 符合专业分析标准
        """

        return self.call_llm(prompt, system_prompt)

    def write_market_viewpoint_analysis(self, viewpoint_result: AnalysisResult) -> str:
        """撰写市场观点分析章节"""
        system_prompt = """你是一位资深证券分析师，请将市场观点整合为专业的研报分析内容。"""

        prompt = f"""
        请基于以下市场观点分析结果撰写专业的市场观点章节：

        {viewpoint_result.content}

        请从以下角度进行分析：
        1. 市场对公司基本面的主流观点
           - 业务发展前景的市场判断
           - 竞争优势的市场认知
           - 财务表现的市场评价

        2. 投资价值的市场共识
           - 估值水平的市场看法
           - 投资亮点的市场关注
           - 风险因素的市场担忧

        3. 分歧观点的深度分析
           - 主要分歧点识别
           - 不同观点的逻辑分析
           - 分歧解决的关键因素

        4. 市场预期与现实的对比
           - 市场预期的合理性评估
           - 超预期或低于预期的可能性
           - 预期修正的催化因素

        要求：
        - 客观呈现市场观点
        - 正确引用观点来源
        - 提供专业的分析判断
        - 突出对投资决策的价值
        """

        return self.call_llm(prompt, system_prompt)

    def write_investment_recommendation(self, all_results: List[AnalysisResult], company_info: CompanyInfo) -> str:
        """撰写投资建议"""
        system_prompt = """你是一位资深投资顾问，请基于全面分析给出专业的投资建议。"""

        # 整合所有分析结果
        comprehensive_analysis = "\n\n".join([f"{r.section}:\n{r.content}" for r in all_results])

        prompt = f"""
        请基于以下全面分析为{company_info.company_name}({company_info.stock_code})给出投资建议：

        {comprehensive_analysis}

        请提供：
        1. 投资评级（强烈推荐/推荐/中性/减持/卖出）
        2. 目标价格区间（如适用）
        3. 投资逻辑总结
        4. 主要投资亮点
        5. 关键风险提示
        6. 适合的投资者类型

        要求：
        - 建议明确、有据可依
        - 风险提示充分
        - 符合监管要求
        - 为投资者提供决策参考
        """

        return self.call_llm(prompt, system_prompt)

class QualityReviewAgent(BaseAgent):
    """质量审查Agent - 负责检查研报质量和规范性"""

    def __init__(self):
        super().__init__("QualityReviewAgent")

    def review_content_quality(self, content: str, section: str) -> Dict[str, Any]:
        """审查内容质量"""
        system_prompt = """你是一位资深的研究报告审查专家，请对研报内容进行专业的质量评估。"""

        prompt = f"""
        请对以下{section}内容进行质量审查：

        {content}

        请从以下维度评估：
        1. 内容完整性（1-10分）
        2. 专业性水平（1-10分）
        3. 逻辑清晰度（1-10分）
        4. 数据支撑度（1-10分）
        5. 语言规范性（1-10分）

        请指出：
        - 主要优点
        - 需要改进的地方
        - 具体修改建议

        以JSON格式返回评估结果。
        """

        review_text = self.call_llm(prompt, system_prompt)

        try:
            # 尝试解析JSON
            import re
            json_match = re.search(r'\{.*\}', review_text, re.DOTALL)
            if json_match:
                return json.loads(json_match.group())
        except:
            pass

        # 如果解析失败，返回默认结果
        return {
            "overall_score": 8.0,
            "review_text": review_text,
            "suggestions": ["内容质量良好，建议继续优化"]
        }

    def check_data_consistency(self, financial_data: Dict[str, pd.DataFrame],
                             analysis_results: List[AnalysisResult]) -> List[str]:
        """检查数据一致性"""
        issues = []

        try:
            # 检查数据来源是否存在
            for result in analysis_results:
                for source in result.data_sources:
                    if not os.path.exists(source):
                        issues.append(f"数据源文件不存在: {source}")

            # 检查财务数据完整性
            required_files = ['主要指标.csv', '利润表.csv']
            for file in required_files:
                if file not in [f for f in os.listdir('.') if f.endswith('.csv')]:
                    issues.append(f"缺少必要的财务数据文件: {file}")

            logger.info(f"数据一致性检查完成，发现{len(issues)}个问题")

        except Exception as e:
            issues.append(f"数据一致性检查失败: {str(e)}")

        return issues

class ResearchReportOrchestrator:
    """研报生成主控制器 - 协调各Agent工作流程"""

    def __init__(self, company_info: CompanyInfo):
        self.company_info = company_info

        self.agents = {
            'data_analysis': DataAnalysisAgent(),
            'market_viewpoint': MarketViewpointAgent(),
            'chart_generation': ChartGenerationAgent(),
            'financial_forecast': FinancialForecastAgent(),
            'content_writing': ContentWritingAgent(),
            'quality_review': QualityReviewAgent()
        }
        self.analysis_results = []
        self.available_charts = []

    def generate_research_report(self) -> str:
        """生成完整研报的主流程"""
        try:
            logger.info("开始生成研究报告...")

            # 1. 数据收集和分析阶段
            logger.info("阶段1: 数据收集和分析")
            financial_data = self.agents['data_analysis'].load_financial_data()
            viewpoint_data = self.agents['market_viewpoint'].load_viewpoint_data()

            # 2. 财务分析
            logger.info("阶段2: 财务分析")
            self.agents['data_analysis'].calculate_financial_ratios()
            financial_analysis = self.agents['data_analysis'].analyze_financial_trends()
            self.analysis_results.append(financial_analysis)

            # 3. 市场观点分析
            logger.info("阶段3: 市场观点分析")
            viewpoint_analysis = self.agents['market_viewpoint'].analyze_market_viewpoints()
            self.analysis_results.append(viewpoint_analysis)

            # 4. 图表管理
            logger.info("阶段4: 图表管理")
            chart_agent = self.agents['chart_generation']
            self.available_charts = chart_agent.load_existing_charts()

            # 5. 财务预测
            logger.info("阶段5: 财务预测")
            forecast_analysis = self.agents['financial_forecast'].generate_financial_forecast(
                financial_data, self.agents['data_analysis'].existing_report)
            self.analysis_results.append(forecast_analysis)

            # 6. 内容撰写
            logger.info("阶段6: 内容撰写")
            content_agent = self.agents['content_writing']

            # 撰写各个章节
            sections = {}
            sections['executive_summary'] = content_agent.write_executive_summary(
                self.company_info, self.analysis_results)
            sections['company_overview'] = content_agent.write_company_overview(self.company_info)
            sections['financial_analysis'] = content_agent.write_financial_analysis(financial_analysis)
            sections['market_viewpoint_analysis'] = content_agent.write_market_viewpoint_analysis(viewpoint_analysis)
            sections['financial_forecast'] = forecast_analysis.content
            sections['investment_recommendation'] = content_agent.write_investment_recommendation(
                self.analysis_results, self.company_info)

            # 7. 质量审查
            logger.info("阶段7: 质量审查")
            quality_agent = self.agents['quality_review']

            # 审查各章节质量
            quality_scores = {}
            for section_name, content in sections.items():
                review = quality_agent.review_content_quality(content, section_name)
                quality_scores[section_name] = review

            # 检查数据一致性
            data_issues = quality_agent.check_data_consistency(financial_data, self.analysis_results)

            # 8. 生成最终文档
            logger.info("阶段8: 生成最终文档")

            # 生成Word文档
            report_path = self._generate_word_document(sections, quality_scores, data_issues)

            logger.info(f"研究报告生成完成: {report_path}")
            return report_path

        except Exception as e:
            logger.error(f"生成研究报告失败: {e}")
            raise

    def _generate_word_document(self, sections: Dict[str, str],
                               quality_scores: Dict[str, Any],
                               data_issues: List[str]) -> str:
        """生成Word文档"""
        try:
            doc = Document()

            # 设置文档样式
            self._setup_document_styles(doc)

            # 1. 封面
            self._add_cover_page(doc)

            # 2. 目录（简化版）
            self._add_table_of_contents(doc)

            # 3. 投资要点
            self._add_section(doc, "投资要点", sections.get('executive_summary', ''))

            # 4. 公司概况
            self._add_section(doc, "公司概况", sections.get('company_overview', ''))

            # 5. 财务分析
            self._add_section(doc, "财务分析", sections.get('financial_analysis', ''))
            self._add_intelligent_charts(doc, "财务分析", sections.get('financial_analysis', ''))

            # 6. 市场观点分析
            self._add_section(doc, "市场观点分析", sections.get('market_viewpoint_analysis', ''))

            # 7. 财务预测
            self._add_section(doc, "财务预测", sections.get('financial_forecast', ''))
            self._add_intelligent_charts(doc, "财务预测", sections.get('financial_forecast', ''))

            # 8. 投资建议
            self._add_section(doc, "投资建议", sections.get('investment_recommendation', ''))

            # 9. 风险提示
            self._add_risk_disclosure(doc)

            # 10. 附录
            self._add_appendix(doc, quality_scores, data_issues)

            # 保存文档
            report_path = "Company_Research_Report.docx"
            doc.save(report_path)

            return report_path

        except Exception as e:
            logger.error(f"生成Word文档失败: {e}")
            raise

    def _setup_document_styles(self, doc: Document):
        """设置文档样式"""
        # 获取文档配置
        doc_config = config.document

        # 设置默认字体为仿宋
        style = doc.styles['Normal']
        style.font.name = doc_config.content_font
        style.font.size = Pt(doc_config.content_font_size)

        # 设置标题样式为仿宋
        heading1 = doc.styles['Heading 1']
        heading1.font.name = doc_config.title_font
        heading1.font.size = Pt(doc_config.heading1_font_size)

        heading2 = doc.styles['Heading 2']
        heading2.font.name = doc_config.title_font
        heading2.font.size = Pt(doc_config.heading2_font_size)

        # 设置表格样式
        try:
            table_style = doc.styles.add_style('Table Style', 1)  # 1 = WD_STYLE_TYPE.TABLE
            table_style.font.name = doc_config.table_font
            table_style.font.size = Pt(doc_config.table_font_size)
        except:
            # 如果样式已存在，则更新现有样式
            pass

    def _add_cover_page(self, doc: Document):
        """添加封面"""
        # 标题
        title = doc.add_heading(f'{self.company_info.company_name}研究报告', 0)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # 副标题
        subtitle = doc.add_paragraph(f'股票代码：{self.company_info.stock_code}')
        subtitle.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # 基本信息表格
        table = doc.add_table(rows=5, cols=2)
        table.alignment = WD_TABLE_ALIGNMENT.CENTER

        info_data = [
            ('公司名称', self.company_info.company_name),
            ('股票代码', self.company_info.stock_code),
            ('所属行业', self.company_info.industry),
            ('上市市场', self.company_info.market),
            ('报告日期', self.company_info.analysis_date)
        ]

        for i, (key, value) in enumerate(info_data):
            table.cell(i, 0).text = key
            table.cell(i, 1).text = value

        # 添加分页符
        doc.add_page_break()

    def _add_table_of_contents(self, doc: Document):
        """添加目录"""
        doc.add_heading('目录', level=1)

        toc_items = [
            '1. 投资要点',
            '2. 公司概况',
            '3. 财务分析',
            '4. 市场表现与投资者情绪',
            '5. 投资建议',
            '6. 风险提示',
            '7. 附录'
        ]

        for item in toc_items:
            doc.add_paragraph(item, style='List Number')

        doc.add_page_break()

    def _add_section(self, doc: Document, title: str, content: str):
        """添加章节"""
        heading = doc.add_heading(title, level=1)
        # 设置标题字体为仿宋
        self._set_paragraph_font(heading, config.document.title_font, config.document.heading1_font_size)

        # 检查是否包含表格
        if self._contains_markdown_table(content):
            # 分别处理表格和非表格内容
            self._add_content_with_tables(doc, content)
        else:
            # 清理markdown格式并分段添加内容
            cleaned_content = self._clean_markdown_content(content)
            paragraphs = cleaned_content.split('\n\n')
            for para in paragraphs:
                if para.strip():
                    # 处理列表项
                    if para.strip().startswith('- ') or para.strip().startswith('• '):
                        lines = para.split('\n')
                        for line in lines:
                            if line.strip():
                                clean_line = line.strip().lstrip('- ').lstrip('• ').strip()
                                if clean_line:
                                    p = doc.add_paragraph(clean_line, style='List Bullet')
                                    self._set_paragraph_font(p, config.document.content_font, config.document.content_font_size)
                    elif para.strip().startswith(('1. ', '2. ', '3. ', '4. ', '5. ')):
                        lines = para.split('\n')
                        for line in lines:
                            if line.strip():
                                # 移除数字前缀
                                import re
                                clean_line = re.sub(r'^\d+\.\s*', '', line.strip())
                                if clean_line:
                                    p = doc.add_paragraph(clean_line, style='List Number')
                                    self._set_paragraph_font(p, config.document.content_font, config.document.content_font_size)
                    else:
                        p = doc.add_paragraph(para.strip())
                        self._set_paragraph_font(p, config.document.content_font, config.document.content_font_size)

    def _clean_markdown_content(self, content: str) -> str:
        """清理markdown格式，但保留表格结构信息"""
        if not content:
            return ""

        import re

        # 移除markdown标题标记
        content = re.sub(r'^#{1,6}\s*', '', content, flags=re.MULTILINE)

        # 移除markdown粗体标记
        content = re.sub(r'\*\*(.*?)\*\*', r'\1', content)
        content = re.sub(r'__(.*?)__', r'\1', content)

        # 移除markdown斜体标记
        content = re.sub(r'\*(.*?)\*', r'\1', content)
        content = re.sub(r'_(.*?)_', r'\1', content)

        # 移除markdown代码块标记
        content = re.sub(r'```.*?```', '', content, flags=re.DOTALL)
        content = re.sub(r'`(.*?)`', r'\1', content)

        # 移除markdown链接标记，保留链接文本
        content = re.sub(r'\[([^\]]+)\]\([^\)]+\)', r'\1', content)

        # 处理表格 - 保留表格内容但转换格式
        content = self._convert_markdown_tables(content)

        # 处理列表项 - 移除markdown列表标记但保留内容
        content = re.sub(r'^[\s]*[-\*\+]\s+', '', content, flags=re.MULTILINE)
        content = re.sub(r'^[\s]*\d+\.\s+', '', content, flags=re.MULTILINE)

        # 移除多余的空格和换行
        content = re.sub(r'\n\s*\n', '\n\n', content)
        content = re.sub(r'[ \t]+', ' ', content)

        # 清理多余的空行
        content = re.sub(r'\n{3,}', '\n\n', content)

        return content.strip()

    def _convert_markdown_tables(self, content: str) -> str:
        """将markdown表格转换为文本格式"""
        import re

        lines = content.split('\n')
        result_lines = []
        in_table = False
        table_rows = []

        for line in lines:
            # 检查是否是表格行
            if '|' in line and line.strip().startswith('|') and line.strip().endswith('|'):
                # 跳过表格分隔符行（如 |---|---|）
                if re.match(r'^\|[\s\-\|]*\|$', line.strip()):
                    continue

                if not in_table:
                    in_table = True
                    table_rows = []

                # 解析表格行
                cells = [cell.strip() for cell in line.strip().split('|')[1:-1]]
                table_rows.append(cells)

            else:
                # 如果之前在处理表格，现在结束表格处理
                if in_table:
                    # 将表格转换为文本格式
                    table_text = self._format_table_as_text(table_rows)
                    result_lines.append(table_text)
                    in_table = False
                    table_rows = []

                result_lines.append(line)

        # 处理文件末尾的表格
        if in_table and table_rows:
            table_text = self._format_table_as_text(table_rows)
            result_lines.append(table_text)

        return '\n'.join(result_lines)

    def _format_table_as_text(self, table_rows: list) -> str:
        """将表格数据格式化为文本"""
        if not table_rows:
            return ""

        # 计算每列的最大宽度
        col_widths = []
        for row in table_rows:
            for i, cell in enumerate(row):
                if i >= len(col_widths):
                    col_widths.append(0)
                # 计算中文字符宽度（中文字符占2个位置）
                width = sum(2 if ord(char) > 127 else 1 for char in cell)
                col_widths[i] = max(col_widths[i], width)

        # 格式化表格
        formatted_rows = []
        for i, row in enumerate(table_rows):
            formatted_cells = []
            for j, cell in enumerate(row):
                if j < len(col_widths):
                    # 计算需要的填充空格
                    cell_width = sum(2 if ord(char) > 127 else 1 for char in cell)
                    padding = col_widths[j] - cell_width
                    formatted_cell = cell + ' ' * padding
                    formatted_cells.append(formatted_cell)
                else:
                    formatted_cells.append(cell)

            formatted_row = '  '.join(formatted_cells)
            formatted_rows.append(formatted_row)

            # 在表头后添加分隔线
            if i == 0:
                separator = '  '.join(['-' * width for width in col_widths])
                formatted_rows.append(separator)

        return '\n'.join(formatted_rows)

    def _contains_markdown_table(self, content: str) -> bool:
        """检查内容是否包含markdown表格"""
        lines = content.split('\n')
        for line in lines:
            if '|' in line and line.strip().startswith('|') and line.strip().endswith('|'):
                return True
        return False

    def _add_content_with_tables(self, doc: Document, content: str):
        """添加包含表格的内容"""
        import re

        lines = content.split('\n')
        current_paragraph = []
        in_table = False
        table_rows = []

        for line in lines:
            # 检查是否是表格行
            if '|' in line and line.strip().startswith('|') and line.strip().endswith('|'):
                # 跳过表格分隔符行
                if re.match(r'^\|[\s\-\|]*\|$', line.strip()):
                    continue

                # 如果之前有段落内容，先添加段落
                if current_paragraph and not in_table:
                    para_text = '\n'.join(current_paragraph).strip()
                    if para_text:
                        cleaned_para = self._clean_markdown_content(para_text)
                        p = doc.add_paragraph(cleaned_para)
                        self._set_paragraph_font(p, config.document.content_font, config.document.content_font_size)
                    current_paragraph = []

                if not in_table:
                    in_table = True
                    table_rows = []

                # 解析表格行
                cells = [cell.strip() for cell in line.strip().split('|')[1:-1]]
                table_rows.append(cells)

            else:
                # 如果之前在处理表格，现在结束表格处理
                if in_table:
                    # 添加Word表格
                    self._add_word_table(doc, table_rows)
                    in_table = False
                    table_rows = []

                # 收集段落内容
                if line.strip():
                    current_paragraph.append(line)
                elif current_paragraph:
                    # 空行表示段落结束
                    para_text = '\n'.join(current_paragraph).strip()
                    if para_text:
                        cleaned_para = self._clean_markdown_content(para_text)
                        p = doc.add_paragraph(cleaned_para)
                        self._set_paragraph_font(p, config.document.content_font, config.document.content_font_size)
                    current_paragraph = []

        # 处理文件末尾的内容
        if in_table and table_rows:
            self._add_word_table(doc, table_rows)
        elif current_paragraph:
            para_text = '\n'.join(current_paragraph).strip()
            if para_text:
                cleaned_para = self._clean_markdown_content(para_text)
                p = doc.add_paragraph(cleaned_para)
                self._set_paragraph_font(p, config.document.content_font, config.document.content_font_size)

    def _add_word_table(self, doc: Document, table_rows: list):
        """添加Word表格"""
        if not table_rows:
            return

        # 创建表格
        table = doc.add_table(rows=len(table_rows), cols=len(table_rows[0]))
        table.style = 'Table Grid'

        # 填充表格数据
        for i, row_data in enumerate(table_rows):
            row = table.rows[i]
            for j, cell_data in enumerate(row_data):
                if j < len(row.cells):
                    cell = row.cells[j]
                    cell.text = cell_data
                    # 设置表格字体为仿宋
                    for paragraph in cell.paragraphs:
                        self._set_paragraph_font(paragraph, config.document.table_font, config.document.table_font_size)

    def _set_paragraph_font(self, paragraph, font_name: str, font_size: int):
        """设置段落字体"""
        try:
            for run in paragraph.runs:
                run.font.name = font_name
                run.font.size = Pt(font_size)

            # 如果段落没有runs，设置段落样式
            if not paragraph.runs and paragraph.text:
                run = paragraph.runs[0] if paragraph.runs else paragraph.add_run()
                run.font.name = font_name
                run.font.size = Pt(font_size)
        except Exception as e:
            logger.warning(f"设置字体失败: {e}")

    def _add_charts_to_section(self, doc: Document, title: str, categories: List[str] = None):
        """添加图表到章节"""
        if not self.available_charts:
            return

        doc.add_heading(title, level=2)

        chart_agent = self.agents['chart_generation']

        if categories:
            # 按类别添加图表
            for category in categories:
                charts = chart_agent.get_chart_by_category(category)
                for chart_path in charts:
                    abs_chart_path = os.path.abspath(chart_path)
                    if os.path.exists(abs_chart_path):
                        try:
                            doc.add_picture(abs_chart_path, width=Inches(6))
                            # 添加图表说明
                            description = chart_agent.get_chart_description(chart_path)
                            clean_description = self._clean_markdown_content(description)
                            caption = doc.add_paragraph(f'图：{clean_description}')
                            caption.alignment = WD_ALIGN_PARAGRAPH.CENTER

                            # 添加数据来源
                            source = doc.add_paragraph('数据来源：东方财富')
                            source.alignment = WD_ALIGN_PARAGRAPH.CENTER

                            doc.add_paragraph("")  # 添加空行
                        except Exception as e:
                            logger.warning(f"插入图表失败: {chart_path}, 错误: {e}")
                            doc.add_paragraph(f"[图表插入失败: {os.path.basename(chart_path)}]")
        else:
            # 添加所有可用图表
            for chart_path in self.available_charts[:5]:  # 限制数量
                abs_chart_path = os.path.abspath(chart_path)
                if os.path.exists(abs_chart_path):
                    try:
                        doc.add_picture(abs_chart_path, width=Inches(6))
                        description = chart_agent.get_chart_description(chart_path)
                        clean_description = self._clean_markdown_content(description)
                        caption = doc.add_paragraph(f'图：{clean_description}')
                        caption.alignment = WD_ALIGN_PARAGRAPH.CENTER

                        # 添加数据来源
                        source = doc.add_paragraph('数据来源：东方财富')
                        source.alignment = WD_ALIGN_PARAGRAPH.CENTER

                        doc.add_paragraph("")  # 添加空行
                    except Exception as e:
                        logger.warning(f"插入图表失败: {chart_path}, 错误: {e}")
                        doc.add_paragraph(f"[图表插入失败: {os.path.basename(chart_path)}]")

    def _add_intelligent_charts(self, doc: Document, content_type: str, content_text: str):
        """智能选择并插入图表"""
        if not self.available_charts:
            return

        chart_agent = self.agents['chart_generation']

        # 智能选择图表
        selected_charts = chart_agent.select_charts_for_content(content_type, content_text)

        if selected_charts:
            doc.add_heading(f"{content_type}相关图表", level=2)

            for chart_path in selected_charts:
                # 确保使用绝对路径
                abs_chart_path = os.path.abspath(chart_path)

                if os.path.exists(abs_chart_path):
                    try:
                        # 插入图片
                        doc.add_picture(abs_chart_path, width=Inches(6))

                        # 添加图表说明
                        chart_name = os.path.basename(chart_path).replace('.png', '')
                        description = chart_agent.chart_descriptions_md.get(
                            chart_name,
                            chart_agent._get_default_description(chart_name)
                        )

                        # 获取与图表相关的分析报告上下文
                        analysis_context = self._get_chart_analysis_context(chart_name, chart_agent)

                        # 清理描述中的markdown格式
                        clean_description = self._clean_markdown_content(description)

                        # 如果有分析上下文，添加到描述中
                        if analysis_context:
                            enhanced_description = f"{clean_description}\n分析要点：{analysis_context}"
                        else:
                            enhanced_description = clean_description

                        caption = doc.add_paragraph(f'图：{enhanced_description}')
                        caption.alignment = WD_ALIGN_PARAGRAPH.CENTER

                        # 添加数据来源
                        source = doc.add_paragraph('数据来源：东方财富')
                        source.alignment = WD_ALIGN_PARAGRAPH.CENTER

                        doc.add_paragraph("")  # 添加空行

                        logger.info(f"成功插入图表: {chart_name}")

                    except Exception as e:
                        logger.error(f"插入图表失败: {chart_path}, 错误: {e}")
                        doc.add_paragraph(f"[图表插入失败: {os.path.basename(chart_path)} - {str(e)}]")
                else:
                    logger.warning(f"图表文件不存在: {abs_chart_path}")
                    doc.add_paragraph(f"[图表文件不存在: {os.path.basename(chart_path)}]")

    def _get_chart_analysis_context(self, chart_name: str, chart_agent) -> str:
        """获取图表相关的分析报告上下文"""
        try:
            if not hasattr(chart_agent, 'analysis_reports') or not chart_agent.analysis_reports:
                return ""

            # 根据图表类型匹配相关的分析内容
            context_keywords = {
                '01_财务概览仪表板': ['财务表现概览', '关键指标', '整体表现'],
                '02_估值对比': ['估值', '市盈率', '市净率', '对比'],
                '02_成长性对比': ['成长性', '增长率', '同比', '环比'],
                '02_规模对比': ['营业收入', '规模', '市场地位'],
                '03_营收利润趋势': ['营业收入', '净利润', '趋势', '增长'],
                '04_盈利能力趋势': ['盈利能力', 'ROE', 'ROA', '毛利率', '净利率'],
                '05_利润表结构分析': ['利润表', '成本', '费用', '结构'],
                '06_现金流分析': ['现金流', '经营', '投资', '筹资'],
                '07_估值分析': ['估值', 'PE', 'PB', '合理性'],
                '08_综合评分雷达图': ['综合', '评分', '多维度', '健康度']
            }

            # 获取图表对应的关键词
            keywords = []
            for key, kw_list in context_keywords.items():
                if key in chart_name:
                    keywords = kw_list
                    break

            if not keywords:
                return ""

            # 从财务分析报告中提取相关内容
            if 'financial_analysis' in chart_agent.analysis_reports:
                content = chart_agent.analysis_reports['financial_analysis']
                relevant_info = self._extract_chart_relevant_info(content, keywords)
                return relevant_info[:200]  # 限制长度

            return ""

        except Exception as e:
            logger.error(f"获取图表分析上下文失败: {e}")
            return ""

    def _extract_chart_relevant_info(self, content: str, keywords: List[str]) -> str:
        """从分析报告中提取与图表相关的信息"""
        lines = content.split('\n')
        relevant_info = []

        for line in lines:
            # 检查是否包含关键词
            if any(keyword in line for keyword in keywords):
                # 清理格式，提取关键信息
                clean_line = line.strip()
                if clean_line and not clean_line.startswith('#') and not clean_line.startswith('|'):
                    # 移除markdown格式
                    clean_line = clean_line.replace('**', '').replace('*', '').replace('`', '')
                    if len(clean_line) > 10:  # 过滤太短的行
                        relevant_info.append(clean_line)

        # 返回前3个最相关的信息点
        return '；'.join(relevant_info[:3]) if relevant_info else ""

    def _add_risk_disclosure(self, doc: Document):
        """添加风险提示"""
        doc.add_heading('风险提示', level=1)

        risk_content = """
        本报告仅供参考，不构成投资建议。投资者应当充分了解投资风险，谨慎投资。

        主要风险因素包括但不限于：
        1. 市场风险：股票价格可能因市场波动而下跌
        2. 行业风险：所属行业可能面临政策变化或竞争加剧
        3. 公司特定风险：经营管理、财务状况等公司特定因素
        4. 流动性风险：股票交易可能面临流动性不足
        5. 信息风险：公开信息可能存在滞后或不完整

        投资者在做出投资决策前，应当仔细阅读相关法律文件，充分了解产品特性和风险。
        """

        doc.add_paragraph(risk_content.strip())

    def _add_appendix(self, doc: Document, quality_scores: Dict[str, Any], data_issues: List[str]):
        """添加附录"""
        doc.add_heading('附录', level=1)

        # 数据来源说明
        doc.add_heading('数据来源', level=2)
        sources = []
        for result in self.analysis_results:
            sources.extend(result.data_sources)

        unique_sources = list(set(sources))
        for source in unique_sources:
            doc.add_paragraph(f"• {source}", style='List Bullet')

        # 市场观点引用
        doc.add_heading('市场观点引用', level=2)
        viewpoint_agent = self.agents['market_viewpoint']
        references = viewpoint_agent.get_viewpoint_references()

        for i, ref in enumerate(references[:10], 1):  # 限制显示前10个引用
            doc.add_paragraph(f"{i}. {ref}", style='List Number')

        # 图表说明
        doc.add_heading('图表说明', level=2)
        chart_agent = self.agents['chart_generation']
        chart_descriptions = chart_agent.get_all_charts_with_descriptions()

        for chart_path, description in chart_descriptions.items():
            chart_name = os.path.basename(chart_path)
            doc.add_paragraph(f"• {chart_name}: {description}", style='List Bullet')

        # 质量评估结果
        doc.add_heading('质量评估', level=2)
        for section, score in quality_scores.items():
            overall_score = score.get('overall_score', 'N/A')
            doc.add_paragraph(f"{section}: {overall_score}分")

        # 数据一致性检查
        if data_issues:
            doc.add_heading('数据一致性问题', level=2)
            for issue in data_issues:
                doc.add_paragraph(f"• {issue}", style='List Bullet')

def main():
    """主函数 - 演示研报生成系统的使用"""
    try:
        # 从环境变量获取公司信息
        company_code = os.environ.get('COMPANY_CODE', '06682.HK')
        company_name = os.environ.get('COMPANY_NAME', '4Paradigm')

        # 根据股票代码推断市场和行业
        if company_code.endswith('.HK'):
            market = "港股"
        elif company_code.endswith('.SZ') or company_code.endswith('.SH'):
            market = "A股"
        else:
            market = "美股"

        # 简单的行业推断（可以后续扩展）
        industry_mapping = {
            '06682.HK': '人工智能/科技',
            '00020.HK': '人工智能/科技',
            '00700.HK': '互联网/科技',
            '09988.HK': '电商/科技',
        }
        industry = industry_mapping.get(company_code, '科技')

        # 配置公司信息
        company_info = CompanyInfo(
            stock_code=company_code,
            company_name=company_name,
            industry=industry,
            market=market,
            analysis_date=datetime.now().strftime("%Y-%m-%d")
        )

        print(f"[配置] 使用公司信息: {company_name} ({company_code}) - {industry} - {market}")

        logger.info("="*50)
        logger.info("智能研报生成系统启动")
        logger.info(f"目标公司: {company_info.company_name} ({company_info.stock_code})")
        logger.info("="*50)

        # 创建主控制器
        orchestrator = ResearchReportOrchestrator(company_info)

        # 生成研报
        report_path = orchestrator.generate_research_report()

        # 输出结果
        print("\n" + "="*60)
        print("[庆祝] 研究报告生成完成！")
        print("="*60)
        print(f"[图表] 报告文件: {report_path}")
        print(f"[趋势] 使用图表: {len(orchestrator.available_charts)}个")
        print(f"[文档] 分析章节: {len(orchestrator.analysis_results)}个")
        print(f"[公司] 目标公司: {company_info.company_name}")
        print(f"[日期] 分析日期: {company_info.analysis_date}")

        # 显示使用的图表
        if orchestrator.available_charts:
            print("\n[图表] 使用的图表文件:")
            for chart in orchestrator.available_charts[:5]:  # 显示前5个
                if os.path.exists(chart):
                    print(f"  [成功] {os.path.basename(chart)}")
                else:
                    print(f"  [失败] {os.path.basename(chart)} (文件不存在)")

        # 系统特性说明
        print("\n[启动] 系统特性:")
        print("  • 多Agent协同工作流程")
        print("  • RAG技术整合财务数据")
        print("  • 自动化图表生成")
        print("  • 专业研报格式输出")
        print("  • 质量审查与数据溯源")
        print("  • 符合证券业规范要求")

        # 技术创新点
        print("\n[想法] 技术创新:")
        print("  • Agent2Agent通信协议")
        print("  • 多模态内容生成")
        print("  • 智能工具调用")
        print("  • 自动化质量控制")
        print("  • 端到端研报生成")

        print("\n[完成] 研报生成任务完成！")
        return report_path

    except Exception as e:
        logger.error(f"研报生成失败: {e}")
        print(f"\n[失败] 错误: {e}")
        print("请检查数据文件是否存在，并确保网络连接正常。")
        return None

def demo_agent_communication():
    """演示Agent间通信机制"""
    print("\n[演示] Agent通信演示:")

    # 创建Agent实例
    data_agent = DataAnalysisAgent()
    viewpoint_agent = MarketViewpointAgent()

    # 演示消息传递
    message1 = data_agent.send_message(
        "MarketViewpointAgent",
        "DATA_READY",
        {"financial_metrics": "已计算完成"}
    )

    message2 = viewpoint_agent.send_message(
        "DataAnalysisAgent",
        "VIEWPOINT_ANALYSIS_COMPLETE",
        {"market_viewpoints": "观点分析完成"}
    )

    print(f"  [发送] {message1.sender} -> {message1.receiver}: {message1.message_type}")
    print(f"  [发送] {message2.sender} -> {message2.receiver}: {message2.message_type}")
    print("  [成功] Agent间通信正常")

def validate_system_requirements():
    """验证系统要求"""
    print("\n[验证] 系统要求验证:")

    requirements = {
        "财务数据文件": ["主要指标.csv", "利润表.csv", "资产负债表.csv"],
        "观点数据文件": ["guba_viewpoint_analysis.json"],
        "配置文件": ["config.py"],
        "Python包": ["pandas", "matplotlib", "docx", "requests"]
    }

    all_good = True

    for category, items in requirements.items():
        print(f"  [清单] {category}:")
        for item in items:
            if category == "Python包":
                try:
                    __import__(item)
                    print(f"    [成功] {item}")
                except ImportError:
                    print(f"    [失败] {item} (未安装)")
                    all_good = False
            else:
                if os.path.exists(item):
                    print(f"    [成功] {item}")
                else:
                    print(f"    [失败] {item} (不存在)")
                    if item.endswith('.csv'):
                        all_good = False

    if all_good:
        print("  [目标] 系统要求验证通过")
    else:
        print("  [警告]  部分要求未满足，可能影响功能")

    return all_good

if __name__ == "__main__":
    print("[系统] 智能研报生成系统")
    print("基于多Agent协同的专业金融研报自动生成")
    print("集成RAG、工具调用、多模态生成等前沿技术")

    # 验证系统要求
    validate_system_requirements()

    # 演示Agent通信
    demo_agent_communication()

    # 生成研报
    result = main()

    if result:
        print(f"\n[文件] 研报已保存至: {result}")
        print("可以使用Microsoft Word打开查看完整报告。")
    else:
        print("\n[想法] 提示:")
        print("1. 确保所有必要的数据文件存在")
        print("2. 检查网络连接和API配置")
        print("3. 查看日志文件了解详细错误信息")
