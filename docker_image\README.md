## 🎯 项目简介

FinAgent 是一个基于大语言模型的智能金融研报生成系统，能够自动收集、分析和生成专业的公司研究报告，以及尚不成熟的宏观经济和行业的研究报告。公司研报生成系统集成了数据爬取、财务分析、市场观点收集、图表生成等多个功能模块，为投资者和分析师提供全面、准确的投资决策支持。

## ✨ 核心功能

### 📊 数据收集与分析
- **财务数据爬取**: 自动获取公司基本信息、财务数据、股价信息
- **市场观点收集**: 从股吧等平台收集投资者观点和市场情绪
- **新闻资讯整合**: 收集相关新闻和行业动态

### 📈 智能分析
- **财务分析**: 基于LLM的深度财务数据分析
- **技术分析**: 股价走势和技术指标分析
- **市场情绪分析**: 投资者观点和市场情绪量化分析

### 📋 报告生成
- **专业研报**: 生成结构化的投资研究报告
- **可视化图表**: 自动生成财务图表和技术分析图
- **多格式输出**: 支持Word、Markdown、PDF等多种格式



### 基本使用


#### 📋 完整格式（明确指定）
```bash
# 明确指定公司名称和股票代码
python run_company_research_report.py --company_name 商汤科技 --company_code 00020.HK
python run_company_research_report.py --company_name 4Paradigm --company_code 06682.HK
python run_company_research_report.py --company_name 腾讯控股 --company_code 00700.HK

# 跳过部分步骤并设置超时
python run_company_research_report.py --company_name 商汤科技 --company_code 00020.HK --skip_steps 1 2 --timeout 1800
```

### 支持的输入格式
- ✅ **公司名称**: 4Paradigm, 平安银行, 腾讯控股
- ✅ **港股代码**: 06682.HK, 00700.HK, 09988.HK

## 📁 核心文件结构

```
FinAgentX/
├── run_company_research_report.py  # 🚀 主程序入口（单参数智能接口）
├── crawler.py                      # 📊 数据爬取模块
├── crawler_viewpoint.py           # 💬 观点爬取模块
├── visualize_report.py            # 📈 图表生成模块
├── financial_agent.py             # 💰 财务分析模块
├── guba_viewpoint_agent.py        # 🗣️ 股吧观点分析模块
├── research_report_generator.py   # 📋 研报生成模块
├── config.py                      # ⚙️ 配置文件
└── README.md                      # 📖 项目说明
```

## 🔧 核心模块详细说明

### 1. 📊 数据爬取模块 (crawler.py)
**功能概述**: 从东方财富等数据源自动爬取公司财务数据和基本信息，耗时10-20秒

**核心特性**:
- **动态公司识别**: 从环境变量读取公司代码，支持港股、A股、美股
- **多维度数据采集**:
  - 主要财务指标 (营收、利润、资产等60+指标)
  - 利润表数据 (收入结构、成本分析等)
  - 资产负债表 (资产结构、负债情况等)
  - 现金流量表 (经营、投资、融资现金流)
  - 行业对比数据 (成长性、估值、规模对比)
- **智能数据映射**: 自动将英文字段映射为中文表头，便于分析
- **数据质量保证**: 自动过滤无效数据，保留核心财务指标
- **容错机制**: 网络异常时自动重试，确保数据完整性

**输出文件**:
- `主要指标.csv` - 核心财务指标汇总
- `利润表.csv` - 详细利润表数据
- `资产负债表.csv` - 资产负债结构
- `现金流量表.csv` - 现金流分析数据
- `成长性对比.csv` - 行业成长性对比
- `估值对比.csv` - 估值水平对比
- `规模对比.csv` - 公司规模对比

### 2. 💬 观点爬取模块 (crawler_viewpoint.py)
**功能概述**: 从东方财富股吧爬取机构账号观点，由于信息较多且为避免反爬设置5-8秒的随机延迟爬取，耗时较长约10-20分钟

**核心特性**:
- **动态URL生成**: 根据股票代码自动生成正确的股吧URL
  - 港股: `00020.HK` → `hk00020` → `https://guba.eastmoney.com/list,hk00020,99,j_1.html`
- **多页面爬取**: 支持批量爬取多个页面的讨论内容
- **完整内容获取**: 不仅获取标题，还深入爬取帖子正文内容
- **反爬虫机制**: 内置随机延迟和请求头伪装，避免被封IP
- **数据结构化**: 提取标题、内容、阅读数、回复数等结构化信息


**输出文件**:
- `guba_viewpoint.csv` - 包含标题、内容、URL、阅读数、回复数等字段

### 3. 📈 图表生成模块 (visualize_report.py)
**功能概述**: 基于爬取的相关财务数据生成专业的可视化图表和分析报告，耗时约2-3分钟

**核心特性**:
- **智能字体管理**: 自动检测和加载中文字体，完美支持中文显示
- **8类专业图表**:
  1. **财务概览仪表板** - 关键指标一览
  2. **估值对比图** - 市盈率、市净率行业对比
  3. **成长性对比图** - 营收、EPS增长率对比
  4. **规模对比图** - 营业收入规模对比
  5. **营收利润趋势图** - 多年度趋势分析
  6. **盈利能力趋势图** - 毛利率、净利率变化
  7. **利润表结构分析** - 收入成本结构饼图
  8. **现金流分析图** - 三大现金流对比
  9. **估值分析图** - 估值指标历史变化
  10. **综合评分雷达图** - 多维度综合评估
- **LLM图表描述**: 为每个图表生成专业的文字描述和分析
- **高质量输出**: 支持PNG格式，300DPI高清输出
- **自适应布局**: 根据数据自动调整图表布局和样式


### 4. 💰 财务分析模块 (financial_agent.py)
**功能概述**: 智能财务数据深度分析，耗时约2-3分钟

**核心特性**:
- **LLM驱动分析**: 使用大模型进行财务数据解读
- **全量数据分析**: 直接分析完整的CSV财务数据，不遗漏任何信息
- **专业报告生成**: 输出结构化的Markdown格式财务分析报告
- **多维度评估**:
  - 财务表现分析 (营收、利润、增长趋势)
  - 盈利能力评估 (毛利率、净利率、ROE等)
  - 成长性分析 (同比增长率、环比变化)
  - 财务健康度 (资产负债率、现金流状况)
  - 估值水平分析 (PE、PB等估值指标)
- **智能洞察**: AI自动识别财务数据中的关键问题和亮点
- **投资建议**: 基于财务分析给出专业的投资建议和风险提示


**输出文件**:
- `财务分析报告_[时间戳].md` - 完整的财务分析报告

### 5. 🗣️ 股吧观点分析模块 (guba_viewpoint_agent.py)
**功能概述**: 对股吧观点数据进行清洗、分析、总结和情绪量化，耗时约10-15分钟

**核心特性**:
- **数据清洗**: 自动过滤无效、重复和垃圾内容
- **情绪分析**: 使用NLP技术分析投资者情绪倾向
- **观点分类**: 将观点分为看多、看空、中性等类别
- **热点提取**: 识别讨论热点和关注焦点
- **情绪量化**: 将定性的市场情绪转化为定量指标
- **批量处理**: 支持大量观点数据的批量分析
- **来源标注**: 保留观点来源，确保分析的可追溯性

**输出文件**:
- `guba_viewpoint_analysis.json` - 结构化的观点分析结果

### 6. 📋 研报生成模块 (research_report_generator.py)
**功能概述**: -整合所有分析结果，生成完整的投资研究报告，耗时约510分钟

**核心特性**:
- **多Agent协同**: 集成财务分析、观点分析、图表等多个模块的结果
- **智能报告结构**: 自动生成专业的研报结构和章节
- **多模态整合**: 结合文字分析、数据图表、市场观点等多种信息
- **投资建议生成**: 基于综合分析给出明确的投资建议和评级
- **风险评估**: 识别和量化投资风险因素
- **多格式输出**: 支持Markdown、Word、PDF等多种格式
- **专业术语**: 使用标准的金融分析术语和表达方式

**报告结构**:
```markdown
1. 执行摘要 - 核心观点和投资建议
2. 公司概况 - 基本信息和业务描述
3. 财务分析 - 详细的财务数据分析
4. 估值分析 - 估值水平和合理价位
5. 技术分析 - 股价走势和技术指标
6. 市场观点 - 投资者情绪和市场预期
7. 风险因素 - 主要风险点识别
8. 投资建议 - 明确的投资评级和建议
```


### 7. 🚀 主程序 (run_company_research_report.py)
**功能概述**: 系统的核心调度器，管理整个研报生成流水线，全程耗时约30-40分钟

**核心特性**:
- **双接口支持**:
  - 完整格式: `python run.py --company_name 商汤科技 --company_code 00020.HK`
- **智能公司识别**: 自动区分公司名称和股票代码，支持中英文
- **流水线管理**: 按序执行6个步骤，支持跳过特定步骤
- **环境变量传递**: 确保所有子模块使用正确的公司信息
- **错误处理**: 单步失败不影响整体流程，提供详细错误信息
- **进度跟踪**: 实时显示执行进度和耗时统计
- **执行报告**: 自动生成详细的执行报告和日志


### 8. ⚙️ 配置管理 (config.py)
**功能概述**: 统一管理系统的所有配置参数

**配置模块**:
- **LLM配置**: API密钥、模型选择、请求参数
- **数据源配置**: 爬取目标、超时设置、重试次数
- **输出配置**: 文件格式、保存路径、图表样式
- **分析配置**: 分析周期、指标阈值、评级标准

## 🏗️ 系统架构与模块交互

### 技术架构图

```mermaid
graph TB
    subgraph "FinAgentX 智能研报生成系统"
        subgraph "🚀 主程序调度器"
            Main[run_company_research_report.py]
            Main --> Interface[双接口支持<br/>简化格式 + 完整格式]
            Main --> Recognition[智能公司识别<br/>名称/代码自动映射]
            Main --> Pipeline[流水线管理<br/>6步骤顺序执行]
            Main --> EnvVar[环境变量传递<br/>统一公司信息]
        end

        subgraph "核心处理模块"
            subgraph "数据采集层"
                Crawler[📊 数据爬取<br/>crawler.py<br/>• 财务数据<br/>• 7个CSV文件<br/>• 行业对比]
                ViewCrawler[💬 观点爬取<br/>crawler_viewpoint.py<br/>• 股吧观点<br/>• 动态URL<br/>• 反爬机制]
                Visualize[📈 图表生成<br/>visualize_report.py<br/>• 10类专业图表<br/>• LLM图表描述<br/>• 中文字体]
            end

            subgraph "分析处理层"
                Financial[💰 财务分析<br/>financial_agent.py<br/>• LLM深度分析<br/>• 专业报告<br/>• 投资建议]
                Sentiment[🗣️ 观点分析<br/>guba_viewpoint_agent.py<br/>• 情绪分析<br/>• 观点分类<br/>• 情绪量化]
                Report[📋 研报生成<br/>research_report_generator.py<br/>• 多Agent协同<br/>• 完整研报<br/>• 多格式输出]
            end
        end

        subgraph "支撑服务"
            Config[⚙️ 配置管理<br/>config.py<br/>• LLM配置<br/>• 统一参数]
            Timeout[🔧 超时管理<br/>timeout_config.py<br/>• 动态超时<br/>• 步骤优化]
            Logging[📝 日志系统<br/>logging & error handling<br/>• 详细追踪]
        end
    end

    %% 数据流向
    Main --> Crawler
    Main --> ViewCrawler
    Crawler --> Visualize
    Crawler --> Financial
    ViewCrawler --> Sentiment
    Visualize --> Report
    Financial --> Report
    Sentiment --> Report

    %% 支撑服务连接
    Config -.-> Main
    Config -.-> Crawler
    Config -.-> Financial
    Timeout -.-> Main
    Logging -.-> Main

    %% 样式定义
    classDef mainClass fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef coreClass fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef supportClass fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px

    class Main,Interface,Recognition,Pipeline,EnvVar mainClass
    class Crawler,ViewCrawler,Visualize,Financial,Sentiment,Report coreClass
    class Config,Timeout,Logging supportClass
```


## ⚙️ 配置说明

### config.py 配置文件
```python
# LLM配置
class LLMConfig:
    model = "deepseek/deepseek-r1-0528:free"
    base_url = "https://openrouter.ai/api/v1"
    api_key = "your_api_key_here"
    temperature = 0.1
    max_tokens = 4000
```

### 必需配置项
1. **API密钥**: 在config.py中设置OpenRouter API密钥;文件中已提供一个api key请勿泄露
2. **模型选择**: 推荐使用deepseek/deepseek-r1-0528:free（免费且效果好）
3. **输出目录**: 确保有写入权限




### 常见问题解决

1. **❌ 缺少必要脚本文件错误**
   ```
   ERROR - ❌ 缺少必要的脚本文件: crawler.py, crawler_viewpoint.py...
   ```
   **解决方案**: 确保在正确的目录下运行
   ```bash
   cd docker_image/docker_image/app
   python run_company_research_report.py 商汤科技
   ```

2. **🌐 观点数据爬取超时**
   ```
   INFO - ❌ 失败 观点数据爬取 - 耗时: 1800.0秒 执行超时
   ```
   **解决方案**:
   - 跳过观点相关步骤: `--skip_steps 2 5`
   - 增加超时时间: `--timeout 2400`
   - 检查网络连接稳定性

3. **🔑 API密钥错误**
   ```
   ERROR - API调用失败: 401 Unauthorized
   ```
   **解决方案**:
   - 检查config.py中的API密钥配置
   - 确认OpenRouter账户有足够额度
   - 验证API密钥格式正确

4. **📊 图表生成中文显示问题**
   ```
   WARNING - 字体加载失败，中文可能显示为方块
   ```
   **解决方案**:
   - Windows: 确保系统有SimHei或Microsoft YaHei字体
   - Linux: 安装中文字体包
   - macOS: 系统自带中文字体支持



### 🎯 优化过程及未来方案
本项目的优化过程主要分为数据输入与报告输出两个方面：

#### 🔢数据输入优化
初期尝试通过 Tushare API 获取港股相关数据，尽管该接口对A股支持良好，但对港股的数据支持存在显著不足。免费部分仅包含股价等基础数据，无法满足赛题对财务数据的完整性要求，且多数金融数据接口在港股覆盖面上普遍较窄。因此，放弃了API方案，转而采用 网页爬虫方式，从东方财富网获取更为详尽的财务数据，包括：三大财务报表、主要财务指标、同业对比数据、机构号观点信息。

在资讯获取方面，最初尝试采集股吧中所有类型的信息，但发现个人号发布的内容多为片面，而资讯新闻多有利好倾向，缺乏客观分析。而机构号通常结合具体财务数据与相关资讯进行研判，内容更具逻辑性与分析价值。因此，最终选定机构号发布的观点作为资讯类输入的主要来源。

#### 📝报告输出优化
在报告生成方面，初期设计为将全部数据输入大语言模型（LLM），由模型直接生成完整分析报告。然而实践中发现，直接生成的文本质量参差不齐，常出现结构混乱、内容冗余或分析深度不足等问题。

为提升报告质量，后续对流程进行如下优化：

1. **引入结构化模板**：制定统一的报告结构，分为章节（如公司概况、财务分析、行业对比、资讯解读、综合结论等），并分步生成各部分内容；

2. **图表支持与文本说明配套**：在生成图表的同时，由大模型配套生成相关的数据说明和分析文字，确保图文内容逻辑一致、相互支撑；

3. **分段控制输出，提高上下文准确性**：避免一次性生成长文，改为模块化逐段生成，并通过中间结果校对保证一致性和连贯性。

通过以上优化，显著提升了报告内容的专业性、逻辑性和可读性。

#### 👐未来优化方案
在当前阶段，FinAgent 的功能主要聚焦于公司研报的生成，已基本建立起较完整的数据采集与分析流程。然而，在行业研报与宏观研报方面，目前尚存在数据支撑不足、信息结构不完善等问题。针对未来的发展，拟从以下几个方向进行优化与扩展：

##### 一、行业研报自动生成

目前缺乏系统性的行业数据与资讯来源，导致行业研报难以深入。后续计划借鉴公司研报的生成策略，优化行业研报流程：

- **代表性公司筛选**：通过大语言模型自动识别并筛选行业内具有代表性的上市公司；
- **数据与资讯获取**：对选定公司进行数据采集，重点获取其机构观点（通常包含行业整体分析）及同业对比数据；
- **模型分析生成**：将上述数据输入模型，通过聚合分析生成覆盖行业整体趋势、竞争格局、发展瓶颈等内容的行业研报。

该方法不仅避免了依赖稀缺的行业级数据，还能充分利用公司层面的数据反映行业特征，实现“以点带面”的分析方式。

##### 二、宏观研报数据支持

针对宏观研报中宏观经济数据与政策资讯的不足，拟从以下两个维度补充数据来源：

- **宏观数据接入**：对接 **世界银行（World Bank）等国际组织的开放数据接口**，获取涵盖GDP、通胀率、就业率、货币供应量等核心宏观指标；
- **资讯抓取增强**：引入 **Google Search API**，基于关键词自动搜索并汇总权威媒体发布的宏观政策、经济趋势等资讯，从而提供新闻语境下的辅助分析。

通过结构化数据与非结构化资讯的融合输入，可实现较为全面、动态的宏观研报生成。

##### 三、公司研报优化方向

尽管公司研报已具备一定深度和完整性，但用户情绪、市场观点等维度仍有提升空间。下一阶段，将重点优化如下方面：

- **情绪分析引入**：系统性爬取东方财富股吧中投资者的个人讨论内容，建立文本情绪识别模型，分析市场对于公司财报、行业事件等的情绪反应；
- **观点分层建模**：区分机构观点与个人观点，分别建模分析，实现更精准的市场解读与信心评估；
- **多源数据融合**：尝试将舆情数据与财务数据融合分析，探索建立基于情绪与基本面双因子的公司评价体系。
