#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
公司研报生成主程序
整合所有功能模块，按顺序执行完整的研报生成流程

使用方法:
python run_company_research_report.py --company_name 4Paradigm --company_code 06682.HK
python run_company_research_report.py --company_name 平安银行 --company_code 000001.SZ
python run_company_research_report.py --company_name 平安银行  # 只提供公司名称
"""

import os
import sys
import argparse
import logging
import time
from datetime import datetime
from typing import Optional, Dict, Any
import subprocess

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('research_report_generation.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class CompanyResearchReportPipeline:
    """公司研报生成流水线"""

    def __init__(self, company_name: str, company_code: str = None, skip_steps: Optional[list] = None):
        self.company_name = company_name
        self.company_code = company_code or company_name  # 如果没有提供代码，使用名称
        self.skip_steps = skip_steps or []
        self.start_time = datetime.now()
        self.execution_log = []

        # 检查配置
        self._check_config()

        # 检查必要文件
        self._check_required_files()

        logger.info(f"🚀 启动公司研报生成流水线")
        logger.info(f"📊 公司名称: {self.company_name}")
        logger.info(f"📈 股票代码: {self.company_code}")
        logger.info(f"⏰ 开始时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        if self.skip_steps:
            logger.info(f"⏭️  跳过步骤: {', '.join(self.skip_steps)}")
    
    def _check_config(self):
        """检查配置文件"""
        try:
            from config import config
            logger.info(f"✅ 配置加载成功")
            logger.info(f"📋 LLM模型: {config.llm.model}")
            logger.info(f"📋 API地址: {config.llm.base_url}")
        except Exception as e:
            logger.error(f"❌ 配置加载失败: {e}")
            raise

    def _check_required_files(self):
        """检查必要的脚本文件"""
        required_scripts = [
            "crawler.py",
            "crawler_viewpoint.py",
            "visualize_report.py",
            "financial_agent.py",
            "guba_viewpoint_agent.py",
            "research_report_generator.py"
        ]

        missing_files = []
        for script in required_scripts:
            if not os.path.exists(script):
                missing_files.append(script)

        if missing_files:
            logger.error(f"❌ 缺少必要的脚本文件: {', '.join(missing_files)}")
            raise FileNotFoundError(f"缺少必要文件: {missing_files}")

        logger.info("✅ 所有必要脚本文件检查通过")

def parse_company_input(company_input: str) -> tuple:
    """简化解析公司输入，返回(公司名称, 股票代码)"""
    company_input = company_input.strip()
    import re

    # 港股代码模式 (如: 06682.HK, 00700.HK)
    hk_pattern = r'^\d{5}\.HK$'
    # A股代码模式 (如: 000001.SZ, 600036.SH)
    a_stock_pattern = r'^\d{6}\.(SZ|SH)$'
    # 美股代码模式 (如: AAPL, MSFT)
    us_stock_pattern = r'^[A-Z]{1,5}$'

    if re.match(hk_pattern, company_input.upper()):
        code = company_input.upper()
        return code, code
    elif re.match(a_stock_pattern, company_input.upper()):
        code = company_input.upper()
        return code, code
    elif re.match(us_stock_pattern, company_input.upper()):
        code = company_input.upper()
        return code, code
    else:
        # 直接返回公司名称和输入
        return company_input, company_input

# 其余代码保持不变
