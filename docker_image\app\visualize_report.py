import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import os
import time
from matplotlib import font_manager
from matplotlib.patches import Rectangle
import matplotlib.patches as mpatches
from datetime import datetime
import warnings
import requests
import json
from config import config
warnings.filterwarnings('ignore')

# 设置中文字体和样式
# 增强的字体加载和调试
plt.rcParams['axes.unicode_minus'] = False

def load_font(font_path, font_name):
    try:
        # 验证字体文件存在
        if not os.path.exists(font_path):
            print(f"字体文件不存在: {font_path}")
            return False
            
        # 加载字体并验证
        font_prop = font_manager.FontProperties(fname=font_path)
        font_manager.fontManager.addfont(font_path)
        
        # 测试字体渲染
        test_fig, test_ax = plt.subplots()
        test_ax.text(0.5, 0.5, '中文测试', fontproperties=font_prop,
                    ha='center', va='center', color='black')
        plt.close(test_fig)
        
        plt.rcParams['font.sans-serif'] = [font_name]
        print(f"[成功] 成功加载并验证字体: {font_name} ({font_path})")
        return True
    except Exception as e:
        print(f"[失败] 字体加载失败 {font_name}: {str(e)}")
        return False

# 字体候选列表
# 增强的字体候选列表
font_candidates = [
    ("SimHei", "C:/Windows/Fonts/simhei.ttf"),
    ("Microsoft YaHei", "C:/Windows/Fonts/msyh.ttc"),
    ("Microsoft YaHei Bold", "C:/Windows/Fonts/msyhbd.ttc"),
    ("Microsoft YaHei Light", "C:/Windows/Fonts/msyhl.ttc"),
    ("Heiti SC Light", "C:/Windows/Fonts/STHeiti Light.ttc"),
    ("Heiti SC Medium", "C:/Windows/Fonts/STHeiti Medium.ttc"),
    ("Arial Unicode MS", "C:/Windows/Fonts/arialuni.ttf")  # Fallback font
]

print("\n=== 字体文件检查 ===")
for font_name, font_path in font_candidates:
    exists = "[存在]" if os.path.exists(font_path) else "[缺失]"
    print(f"{exists} {font_name}: {font_path}")
print("==================\n")

# 尝试加载字体
font_loaded = False
current_font = None
font_path_loaded = None
for font_name, font_path in font_candidates:
    if os.path.exists(font_path):
        if load_font(font_path, font_name):
            font_loaded = True
            current_font = font_name
            font_path_loaded = font_path
            break

if not font_loaded:
    print("[警告] 严重: 未能加载任何中文字体，将使用默认字体")
    current_font = plt.rcParams['font.sans-serif'][0] if plt.rcParams['font.sans-serif'] else 'Arial'
    font_path_loaded = None
else:
    print(f"[当前] 当前使用字体: {current_font}")

# 强制设置 matplotlib 全局字体属性，确保所有图表都用中文字体
if font_path_loaded:
    font_prop_global = font_manager.FontProperties(fname=font_path_loaded)
    plt.rcParams['font.sans-serif'] = [current_font]
    plt.rcParams['font.family'] = 'sans-serif'
    plt.rcParams['font.sans-serif'] = [current_font]
    plt.rcParams['axes.unicode_minus'] = False
else:
    font_prop_global = None

# 强制设置文本颜色为黑色
plt.rcParams['text.color'] = 'black'
plt.rcParams['axes.labelcolor'] = 'black'
plt.rcParams['xtick.color'] = 'black'
plt.rcParams['ytick.color'] = 'black'

# 确保文本颜色与背景有足够对比度
# 强制文本和背景颜色设置
plt.rcParams.update({
    'text.color': 'black',
    'axes.labelcolor': 'black',
    'xtick.color': 'black',
    'ytick.color': 'black',
    'figure.facecolor': 'white',
    'axes.facecolor': 'white',
    'savefig.facecolor': 'white'  # 确保保存的图片也有白色背景
})

# 专业配色方案
COLORS = {
    'primary': '#1f77b4',      # 主色调 - 蓝色
    'secondary': '#ff7f0e',    # 次要色 - 橙色
    'success': '#2ca02c',      # 成功色 - 绿色
    'warning': '#d62728',      # 警告色 - 红色
    'info': '#9467bd',         # 信息色 - 紫色
    'light': '#7f7f7f',        # 浅色 - 灰色
    'background': '#f8f9fa',   # 背景色
    'grid': '#e9ecef'          # 网格色
}

# 图表描述生成器类
class ChartDescriptionGenerator:
    """图表描述生成器 - 使用LLM生成专业的图表描述"""

    def __init__(self):
        self.llm_config = config.llm
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Bearer {self.llm_config.api_key}',
            'Content-Type': 'application/json'
        })
        self.descriptions = {}
        self.request_count = 0  # 请求计数器
        self.last_request_time = 0  # 上次请求时间

    def _wait_for_rate_limit(self):
        """等待以避免速率限制"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time

        # 基础间隔：每次请求至少间隔3秒
        min_interval = 3.0

        # 根据请求次数增加间隔
        if self.request_count > 5:
            min_interval = 5.0  # 5次后增加到5秒
        if self.request_count > 10:
            min_interval = 8.0  # 10次后增加到8秒

        if time_since_last < min_interval:
            wait_time = min_interval - time_since_last
            print(f"⏰ 等待 {wait_time:.1f} 秒以避免速率限制...")
            time.sleep(wait_time)

        self.last_request_time = time.time()
        self.request_count += 1

    def call_llm(self, prompt: str, max_retries: int = 3) -> str:
        """调用LLM生成描述，包含重试机制"""
        for attempt in range(max_retries):
            try:
                # 等待以避免速率限制
                self._wait_for_rate_limit()

                payload = {
                    "model": self.llm_config.model,
                    "messages": [
                        {
                            "role": "system",
                            "content": "你是一位专业的金融分析师，擅长解读财务图表。请用专业、简洁的语言描述图表内容和分析价值。"
                        },
                        {
                            "role": "user",
                            "content": prompt
                        }
                    ],
                    "max_tokens": 500,
                    "temperature": 0.3
                }

                print(f"🔄 正在调用LLM生成描述... (尝试 {attempt + 1}/{max_retries})")

                response = self.session.post(
                    f"{self.llm_config.base_url}/chat/completions",
                    json=payload,
                    timeout=60  # 增加超时时间
                )

                # 检查HTTP状态码
                if response.status_code == 429:
                    print(f"⚠️  遇到429错误，等待更长时间后重试...")
                    retry_delay = (attempt + 1) * 10  # 递增延迟：10s, 20s, 30s
                    time.sleep(retry_delay)
                    continue

                response.raise_for_status()
                result = response.json()

                if 'choices' in result and len(result['choices']) > 0:
                    content = result['choices'][0]['message']['content']
                    print(f"[成功] LLM调用成功")
                    return content
                else:
                    print(f"[警告] LLM返回格式异常: {result}")
                    if attempt < max_retries - 1:
                        time.sleep(5)
                        continue
                    return "图表描述生成失败：返回格式异常"

            except requests.exceptions.Timeout:
                print(f"⏰ LLM调用超时 (尝试 {attempt + 1}/{max_retries})")
                if attempt < max_retries - 1:
                    time.sleep(10)
                    continue
            except requests.exceptions.HTTPError as e:
                if e.response.status_code == 429:
                    print(f"[警告] 速率限制错误 (尝试 {attempt + 1}/{max_retries})")
                    retry_delay = (attempt + 1) * 15  # 更长的延迟
                    time.sleep(retry_delay)
                    continue
                else:
                    print(f"[失败] HTTP错误: {e}")
                    if attempt < max_retries - 1:
                        time.sleep(5)
                        continue
            except Exception as e:
                print(f"[失败] LLM调用异常 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(5)
                    continue

        print(f"[失败] LLM调用最终失败，已重试{max_retries}次")
        return "图表描述生成失败"

    def generate_description(self, chart_name: str, chart_type: str, data_summary: str) -> str:
        """生成图表描述"""
        prompt = f"""
        请为以下财务图表生成专业的描述：

        图表名称：{chart_name}
        图表类型：{chart_type}
        数据概要：{data_summary}

        请从以下角度描述：
        1. 图表展示的核心内容
        2. 关键数据指标和趋势
        3. 对投资分析的价值
        4. 适合在研报中的使用场景

        要求：
        - 语言专业、简洁
        - 突出分析价值
        - 150字以内
        """

        description = self.call_llm(prompt)
        self.descriptions[chart_name] = description
        return description

    def save_descriptions_to_markdown(self, output_path: str = "chart_descriptions.md"):
        """保存所有图表描述到markdown文件"""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write("# 财务图表描述文档\n\n")
                f.write("本文档包含所有财务分析图表的专业描述，用于研报生成时的智能图表选择。\n\n")
                f.write(f"生成时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("数据来源：东方财富\n\n")
                f.write("---\n\n")

                for chart_name, description in self.descriptions.items():
                    f.write(f"## {chart_name}\n\n")
                    f.write(f"**描述**：{description}\n\n")
                    f.write(f"**数据来源**：东方财富\n\n")
                    f.write("---\n\n")

            print(f"[成功] 图表描述已保存到: {output_path}")

        except Exception as e:
            print(f"[失败] 保存图表描述失败: {e}")

# 全局图表描述生成器
chart_desc_generator = ChartDescriptionGenerator()

def add_data_source_annotation(fig, ax=None):
    """为图表添加数据来源标注"""
    if ax is None:
        # 如果没有指定ax，在整个figure上添加
        fig.text(0.99, 0.01, '数据来源：东方财富',
                ha='right', va='bottom', fontsize=8,
                color='gray', alpha=0.7)
    else:
        # 在指定的ax上添加
        ax.text(0.99, 0.01, '数据来源：东方财富',
               transform=ax.transAxes, ha='right', va='bottom',
               fontsize=8, color='gray', alpha=0.7)

# 文件名定义
main_file = '主要指标.csv'
growth_file = '成长性对比.csv'
valuation_file = '估值对比.csv'
scale_file = '规模对比.csv'
income_file = '利润表.csv'
balance_file = '资产负债表.csv'
cashflow_file = '现金流量表.csv'

# 输出图片目录
def ensure_dir(path):
    if not os.path.exists(path):
        os.makedirs(path)
img_dir = 'report_images'
ensure_dir(img_dir)

# 读取数据
def read_csv(filename):
    try:
        if os.path.exists(filename):
            return pd.read_csv(filename, encoding='utf-8-sig')
        else:
            print(f"文件 {filename} 不存在")
            return pd.DataFrame()
    except Exception as e:
        print(f"读取文件 {filename} 失败: {e}")
        return pd.DataFrame()

# 读取所有数据文件
main_df = read_csv(main_file)
growth_df = read_csv(growth_file)
valuation_df = read_csv(valuation_file)
scale_df = read_csv(scale_file)
income_df = read_csv(income_file)
balance_df = read_csv(balance_file)
cashflow_df = read_csv(cashflow_file)

# 获取公司基本信息
def get_company_info():
    if not main_df.empty:
        company_name = main_df['股票简称'].iloc[0] if '股票简称' in main_df.columns else "目标公司"
        stock_code = main_df['证券代码'].iloc[0] if '证券代码' in main_df.columns else ""
        return company_name, stock_code
    return "目标公司", ""

company_name, stock_code = get_company_info()

# 数据处理函数
def format_number(value, unit='万元'):
    """格式化数字显示"""
    if pd.isna(value) or value == 0:
        return "0"

    if abs(value) >= 1e8:
        return f"{value/1e8:.1f}亿"
    elif abs(value) >= 1e4:
        return f"{value/1e4:.1f}万"
    else:
        return f"{value:.1f}"

def safe_convert_numeric(series):
    """安全转换为数值类型"""
    return pd.to_numeric(series, errors='coerce')

# 设置图表样式
def set_chart_style():
    """设置专业的图表样式和中文字体"""
    plt.style.use('default')
    sns.set_palette("husl")
    if font_prop_global:
        plt.rcParams['font.sans-serif'] = [current_font]
        plt.rcParams['font.family'] = 'sans-serif'
        plt.rcParams['axes.unicode_minus'] = False

# 1. 财务概览仪表板
def create_financial_dashboard():
    """创建财务概览仪表板"""
    if main_df.empty:
        return

    # 获取最新财务数据
    latest_data = main_df.iloc[0] if not main_df.empty else None
    if latest_data is None:
        return

    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle(f'{company_name} ({stock_code}) 财务概览', fontsize=20, fontweight='bold', y=0.95)

    # 1.1 营收与利润趋势
    if '报告期' in main_df.columns:
        periods = main_df['报告期'].tolist()
        revenue = safe_convert_numeric(main_df['营业收入']).tolist()
        profit = safe_convert_numeric(main_df['归母净利润']).tolist()

        ax1_twin = ax1.twinx()
        line1 = ax1.plot(periods, revenue, marker='o', linewidth=3, color=COLORS['primary'], label='营业收入')
        line2 = ax1_twin.plot(periods, profit, marker='s', linewidth=3, color=COLORS['warning'], label='归母净利润')

        ax1.set_title('营收与利润趋势', fontsize=14, fontweight='bold', pad=20)
        ax1.set_ylabel('营业收入 (元)', color=COLORS['primary'], fontsize=12)
        ax1_twin.set_ylabel('归母净利润 (元)', color=COLORS['warning'], fontsize=12)
        ax1.tick_params(axis='x', rotation=45)
        ax1.grid(True, alpha=0.3)

        # 合并图例
        lines = line1 + line2
        labels = [l.get_label() for l in lines]
        ax1.legend(lines, labels, loc='upper left')

    # 1.2 盈利能力指标
    profitability_metrics = ['毛利率', '净利率', '加权净资产收益率']
    values = []
    labels = []
    for metric in profitability_metrics:
        if metric in main_df.columns:
            val = safe_convert_numeric(main_df[metric]).iloc[0] if not main_df.empty else 0
            if not pd.isna(val):
                values.append(val)
                labels.append(metric)

    if values:
        colors = [COLORS['success'] if v > 0 else COLORS['warning'] for v in values]
        bars = ax2.bar(labels, values, color=colors, alpha=0.8)
        ax2.set_title('盈利能力指标 (%)', fontsize=14, fontweight='bold', pad=20)
        ax2.set_ylabel('百分比 (%)', fontsize=12)
        ax2.tick_params(axis='x', rotation=45)
        ax2.grid(True, alpha=0.3, axis='y')

        # 添加数值标签
        for bar, val in zip(bars, values):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + (max(values) * 0.01),
                    f'{val:.1f}%', ha='center', va='bottom', fontweight='bold')

    # 1.3 资产负债结构
    if not balance_df.empty:
        # 从资产负债表获取主要科目
        balance_pivot = balance_df.pivot_table(
            index='报告期',
            columns='标准科目名称',
            values='金额',
            aggfunc='sum'
        ).fillna(0)

        if not balance_pivot.empty:
            latest_balance = balance_pivot.iloc[0]
            # 选择主要资产科目
            asset_items = ['货币资金', '应收账款', '存货', '固定资产', '无形资产']
            asset_values = []
            asset_labels = []

            for item in asset_items:
                matching_cols = [col for col in latest_balance.index if item in col]
                if matching_cols:
                    val = latest_balance[matching_cols[0]]
                    if val > 0:
                        asset_values.append(val)
                        asset_labels.append(item)

            if asset_values:
                wedges, texts, autotexts = ax3.pie(asset_values, labels=asset_labels, autopct='%1.1f%%',
                                                  colors=plt.cm.Set3.colors, startangle=90)
                ax3.set_title('资产结构分析', fontsize=14, fontweight='bold', pad=20)

    # 1.4 现金流分析
    if '经营活动现金流净额' in main_df.columns and '投资活动现金流净额' in main_df.columns:
        periods = main_df['报告期'].tolist()
        operating_cf = safe_convert_numeric(main_df['经营活动现金流净额']).tolist()
        investing_cf = safe_convert_numeric(main_df['投资活动现金流净额']).tolist()
        financing_cf = safe_convert_numeric(main_df['融资活动现金流净额']).tolist() if '融资活动现金流净额' in main_df.columns else [0] * len(periods)

        x = range(len(periods))
        width = 0.25

        ax4.bar([i - width for i in x], operating_cf, width, label='经营现金流', color=COLORS['success'], alpha=0.8)
        ax4.bar(x, investing_cf, width, label='投资现金流', color=COLORS['info'], alpha=0.8)
        ax4.bar([i + width for i in x], financing_cf, width, label='融资现金流', color=COLORS['secondary'], alpha=0.8)

        ax4.set_title('现金流分析', fontsize=14, fontweight='bold', pad=20)
        ax4.set_ylabel('现金流 (元)', fontsize=12)
        ax4.set_xticks(x)
        ax4.set_xticklabels(periods, rotation=45)
        ax4.legend()
        ax4.grid(True, alpha=0.3, axis='y')
        ax4.axhline(y=0, color='black', linestyle='-', alpha=0.5)

    plt.tight_layout()

    # 添加数据来源标注
    add_data_source_annotation(fig)

    chart_path = f'{img_dir}/01_财务概览仪表板.png'
    plt.savefig(chart_path, dpi=300, bbox_inches='tight')

    # 生成图表描述
    data_summary = f"营业收入: {format_number(latest_data.get('营业收入', 0))}万元, " \
                  f"净利润: {format_number(latest_data.get('净利润', 0))}万元, " \
                  f"ROE: {latest_data.get('ROE', 0):.2f}%, " \
                  f"毛利率: {latest_data.get('毛利率', 0):.2f}%"

    chart_desc_generator.generate_description(
        "01_财务概览仪表板",
        "仪表板/综合图表",
        data_summary
    )

    plt.close()
    print("[完成] 财务概览仪表板已生成")

# 2. 行业对比分析
def create_industry_comparison():
    """创建行业对比分析图表"""
    compare_datasets = [
        ('规模对比', scale_df, ['总市值', '营业收入', '归母净利润']),
        ('成长性对比', growth_df, ['营业收入同比增长率', 'EPS同比增长率']),
        ('估值对比', valuation_df, ['市盈率TTM', '市净率TTM'])
    ]

    for title, df, indicators in compare_datasets:
        if df.empty:
            continue

        for ind in indicators:
            if ind not in df.columns or '对比证券名称' not in df.columns or '数据来源' not in df.columns:
                continue

            # 检查目标公司数据
            target_row = df[(df['数据来源'] == '目标公司')]
            if target_row.empty:
                continue

            fig = plt.figure(figsize=(14, 8))

            # 准备数据
            plot_df = df[df['数据来源'].isin(['目标公司', '行业平均对比'])].copy()
            peers_df = df[df['数据来源'] == '同业公司对比'].copy()

            if not peers_df.empty and ind in peers_df.columns:
                peers_df = peers_df.sort_values(ind, ascending=False).head(8)
                plot_df = pd.concat([plot_df, peers_df], ignore_index=True)

            # 专业配色
            palette = {
                '目标公司': COLORS['primary'],
                '行业平均对比': COLORS['secondary'],
                '同业公司对比': COLORS['light']
            }

            # 创建柱状图
            ax = sns.barplot(data=plot_df, x='对比证券名称', y=ind, hue='数据来源',
                           palette=palette, alpha=0.8)

            # 设置标题和标签
            plt.title(f'{company_name} - {ind} 行业对比分析', fontsize=16, fontweight='bold', pad=20)
            plt.xlabel('公司/行业', fontsize=12)
            plt.ylabel(ind, fontsize=12)
            plt.xticks(rotation=45, ha='right')

            # 添加数值标签
            for p in ax.patches:
                height = p.get_height()
                if not pd.isna(height) and height != 0:
                    ax.annotate(f'{height:.1f}',
                              (p.get_x() + p.get_width() / 2, height),
                              ha='center', va='bottom', fontsize=10, fontweight='bold')

            # 添加网格和图例
            ax.grid(True, alpha=0.3, axis='y')
            ax.legend(title='数据来源', title_fontsize=12, fontsize=11)

            plt.tight_layout()

            # 添加数据来源标注
            add_data_source_annotation(fig)

            chart_path = f'{img_dir}/02_{title}_{ind}.png'
            plt.savefig(chart_path, dpi=300, bbox_inches='tight')

            # 生成图表描述 - 包含实际对比数据
            data_summary = f"{title}中{ind}指标的行业对比分析"

            # 添加实际对比数据信息
            if not plot_df.empty and ind in plot_df.columns:
                # 获取数据统计信息
                valid_data = plot_df[plot_df[ind].notna()]

                if len(valid_data) > 0:
                    values = valid_data[ind].tolist()
                    companies = valid_data['对比证券名称'].tolist()

                    max_value = max(values)
                    min_value = min(values)
                    avg_value = sum(values) / len(values)

                    # 找到最高和最低的公司
                    max_idx = values.index(max_value)
                    min_idx = values.index(min_value)
                    max_company = companies[max_idx]
                    min_company = companies[min_idx]

                    # 获取目标公司数据
                    target_data = valid_data[valid_data['数据来源'] == '目标公司']
                    target_value = target_data[ind].iloc[0] if not target_data.empty else 0

                    # 判断指标类型来确定单位
                    unit = "%" if "增长率" in ind or "率" in ind else ("倍" if "市盈率" in ind else "亿元")

                    data_summary = f"""{title}中{ind}指标的行业对比分析：
                    - 对比范围：{len(companies)}家同行业公司
                    - 目标公司：{target_value:.2f}{unit}
                    - 最高值：{max_company} {max_value:.2f}{unit}
                    - 最低值：{min_company} {min_value:.2f}{unit}
                    - 行业均值：{avg_value:.2f}{unit}
                    - 相对位置：{'领先' if target_value > avg_value else '落后'}于行业均值
                    - 分析价值：{'识别行业龙头和落后者' if '规模' in title else '评估成长性差异' if '成长性' in title else '判断估值合理性'}"""

            chart_desc_generator.generate_description(
                f"02_{title}_{ind}",
                "柱状图/对比分析",
                data_summary
            )

            plt.close()

# 3. 财务指标趋势分析
def create_financial_trends():
    """创建详细的财务指标趋势分析"""
    if main_df.empty or '报告期' not in main_df.columns:
        return

    df = main_df.copy().sort_values('报告期')

    # 3.1 营收和利润趋势
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
    fig.suptitle(f'{company_name} 营收与盈利趋势分析', fontsize=16, fontweight='bold')

    # 营收趋势
    if '营业收入' in df.columns:
        revenue = safe_convert_numeric(df['营业收入'])
        periods = df['报告期']

        ax1.plot(periods, revenue, marker='o', linewidth=3, markersize=8,
                color=COLORS['primary'], label='营业收入')
        ax1.fill_between(periods, revenue, alpha=0.3, color=COLORS['primary'])
        ax1.set_title('营业收入趋势', fontsize=14, fontweight='bold')
        ax1.set_ylabel('营业收入 (元)', fontsize=12)
        ax1.tick_params(axis='x', rotation=45)
        ax1.grid(True, alpha=0.3)

        # 添加数值标签
        for x, y in zip(periods, revenue):
            if not pd.isna(y):
                ax1.text(x, y, format_number(y), ha='center', va='bottom',
                        fontsize=10, fontweight='bold')

    # 利润趋势
    if '归母净利润' in df.columns:
        profit = safe_convert_numeric(df['归母净利润'])

        colors = [COLORS['success'] if p >= 0 else COLORS['warning'] for p in profit]
        bars = ax2.bar(periods, profit, color=colors, alpha=0.8)
        ax2.set_title('归母净利润趋势', fontsize=14, fontweight='bold')
        ax2.set_ylabel('归母净利润 (元)', fontsize=12)
        ax2.tick_params(axis='x', rotation=45)
        ax2.grid(True, alpha=0.3, axis='y')
        ax2.axhline(y=0, color='black', linestyle='-', alpha=0.5)

        # 添加数值标签
        for bar, val in zip(bars, profit):
            if not pd.isna(val):
                height = bar.get_height()
                ax2.text(bar.get_x() + bar.get_width()/2.,
                        height + (abs(height) * 0.05 if height >= 0 else -abs(height) * 0.05),
                        format_number(val), ha='center',
                        va='bottom' if height >= 0 else 'top',
                        fontsize=10, fontweight='bold')

    plt.tight_layout()

    # 添加数据来源标注
    add_data_source_annotation(fig)

    chart_path = f'{img_dir}/03_营收利润趋势.png'
    plt.savefig(chart_path, dpi=300, bbox_inches='tight')

    # 生成图表描述 - 包含实际数据信息
    data_summary = "营业收入和净利润的历史趋势变化分析"

    # 添加实际数据信息
    if '营业收入' in df.columns and '归母净利润' in df.columns:
        revenue_data = safe_convert_numeric(df['营业收入'])
        profit_data = safe_convert_numeric(df['归母净利润'])
        periods = df['报告期'].tolist()

        # 计算关键指标
        if len(revenue_data) > 1:
            revenue_growth = ((revenue_data.iloc[-1] - revenue_data.iloc[0]) / revenue_data.iloc[0] * 100) if revenue_data.iloc[0] != 0 else 0
            latest_revenue = revenue_data.iloc[-1]
            latest_profit = profit_data.iloc[-1]

            data_summary = f"""营业收入和净利润趋势分析：
            - 时间跨度：{periods[0]}至{periods[-1]}
            - 最新营业收入：{format_number(latest_revenue)}元
            - 最新归母净利润：{format_number(latest_profit)}元
            - 营收增长率：{revenue_growth:.1f}%
            - 盈利状况：{'盈利' if latest_profit > 0 else '亏损'}
            - 趋势特征：营收{'增长' if revenue_growth > 0 else '下降'}，利润{'改善' if latest_profit > profit_data.iloc[0] else '恶化'}"""

    chart_desc_generator.generate_description(
        "03_营收利润趋势",
        "折线图和柱状图组合/趋势分析",
        data_summary
    )

    plt.close()

    # 3.2 盈利能力指标趋势
    profitability_indicators = ['毛利率', '净利率', '加权净资产收益率']
    available_indicators = [ind for ind in profitability_indicators if ind in df.columns]

    if available_indicators:
        fig = plt.figure(figsize=(14, 8))

        for i, indicator in enumerate(available_indicators):
            values = safe_convert_numeric(df[indicator])
            plt.plot(periods, values, marker='o', linewidth=3, markersize=8,
                    label=indicator, color=list(COLORS.values())[i])

        plt.title(f'{company_name} 盈利能力指标趋势', fontsize=16, fontweight='bold', pad=20)
        plt.xlabel('报告期', fontsize=12)
        plt.ylabel('百分比 (%)', fontsize=12)
        plt.xticks(rotation=45)
        plt.legend(fontsize=12)
        plt.grid(True, alpha=0.3)
        plt.axhline(y=0, color='black', linestyle='-', alpha=0.5)

        plt.tight_layout()

        # 添加数据来源标注
        add_data_source_annotation(fig)

        chart_path = f'{img_dir}/04_盈利能力趋势.png'
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')

        # 生成图表描述 - 包含实际数据信息
        data_summary = f"盈利能力指标趋势：{', '.join(available_indicators)}"

        # 添加实际数据信息
        if available_indicators:
            periods = df['报告期'].tolist()
            latest_values = {}
            trends = {}

            for indicator in available_indicators:
                values = safe_convert_numeric(df[indicator])
                if len(values) > 0:
                    latest_values[indicator] = values.iloc[-1]
                    if len(values) > 1:
                        trend = "上升" if values.iloc[-1] > values.iloc[0] else "下降"
                        trends[indicator] = trend

            data_summary = f"""盈利能力指标趋势分析：
            - 分析指标：{', '.join(available_indicators)}
            - 时间跨度：{periods[0]}至{periods[-1]}
            - 最新数值：{', '.join([f'{k}={v:.2f}%' for k, v in latest_values.items()])}
            - 趋势特征：{', '.join([f'{k}{v}' for k, v in trends.items()])}
            - 盈利质量：{'良好' if latest_values.get('净利率', 0) > 5 else '一般' if latest_values.get('净利率', 0) > 0 else '较差'}"""

        chart_desc_generator.generate_description(
            "04_盈利能力趋势",
            "折线图/趋势分析",
            data_summary
        )

        plt.close()

# 4. 财务报表结构分析
def create_financial_structure_analysis():
    """创建财务报表结构分析"""

    # 4.1 利润表结构分析
    if not income_df.empty:
        # 获取最新期间的利润表数据
        latest_period = income_df['报告期'].max()
        latest_income = income_df[income_df['报告期'] == latest_period]

        # 主要利润表科目
        key_items = ['营业额', '销售成本', '毛利', '营业费用', '管理费用', '研发费用']

        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
        fig.suptitle(f'{company_name} 利润表结构分析 ({latest_period})', fontsize=16, fontweight='bold')

        # 收入成本结构
        income_items = []
        income_values = []

        for item in key_items:
            matching_rows = latest_income[latest_income['标准科目名称'].str.contains(item, na=False)]
            if not matching_rows.empty:
                value = matching_rows['金额'].sum()
                if abs(value) > 0:
                    income_items.append(item)
                    income_values.append(abs(value))

        if income_items:
            colors = plt.cm.Set3.colors[:len(income_items)]
            ax1.pie(income_values, labels=income_items, autopct='%1.1f%%',
                   colors=colors, startangle=90)
            ax1.set_title('收入成本结构', fontsize=14, fontweight='bold')

        # 费用结构分析
        expense_items = ['营业费用', '管理费用', '研发费用', '财务费用']
        expense_values = []
        expense_labels = []

        for item in expense_items:
            matching_rows = latest_income[latest_income['标准科目名称'].str.contains(item, na=False)]
            if not matching_rows.empty:
                value = abs(matching_rows['金额'].sum())
                if value > 0:
                    expense_values.append(value)
                    expense_labels.append(item)

        if expense_values:
            bars = ax2.bar(expense_labels, expense_values,
                          color=[COLORS['warning'], COLORS['info'], COLORS['success'], COLORS['secondary']][:len(expense_values)],
                          alpha=0.8)
            ax2.set_title('费用结构分析', fontsize=14, fontweight='bold')
            ax2.set_ylabel('金额 (元)', fontsize=12)
            ax2.tick_params(axis='x', rotation=45)

            # 添加数值标签
            for bar, val in zip(bars, expense_values):
                height = bar.get_height()
                ax2.text(bar.get_x() + bar.get_width()/2., height,
                        format_number(val), ha='center', va='bottom',
                        fontsize=10, fontweight='bold')

        plt.tight_layout()

        # 添加数据来源标注
        add_data_source_annotation(fig)

        chart_path = f'{img_dir}/05_利润表结构分析.png'
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')

        # 生成图表描述 - 包含实际结构数据
        data_summary = "利润表主要科目的结构分析和变化趋势"

        # 添加实际结构数据信息
        if income_items and expense_values:
            # 收入成本结构信息
            total_income = sum(income_values)
            income_structure = []
            for i, (item, value) in enumerate(zip(income_items, income_values)):
                percentage = (value / total_income * 100) if total_income > 0 else 0
                income_structure.append(f"{item}占比{percentage:.1f}%")

            # 费用结构信息
            total_expense = sum(expense_values)
            expense_structure = []
            for item, value in zip(expense_labels, expense_values):
                percentage = (value / total_expense * 100) if total_expense > 0 else 0
                expense_structure.append(f"{item}{format_number(value)}元({percentage:.1f}%)")

            # 计算关键比率
            revenue = income_values[0] if income_items and '营业收入' in income_items[0] else total_income
            cost_ratio = (income_values[1] / revenue * 100) if len(income_values) > 1 and revenue > 0 else 0
            expense_ratio = (total_expense / revenue * 100) if revenue > 0 else 0

            data_summary = f"""利润表结构分析：
            - 收入结构：{', '.join(income_structure[:3])}
            - 费用结构：{', '.join(expense_structure)}
            - 成本率：{cost_ratio:.1f}%
            - 费用率：{expense_ratio:.1f}%
            - 结构特征：{'成本控制良好' if cost_ratio < 70 else '成本压力较大'}，{'费用管控有效' if expense_ratio < 30 else '费用偏高'}
            - 分析价值：识别盈利结构变化，评估成本费用管控效果，判断盈利质量"""

        chart_desc_generator.generate_description(
            "05_利润表结构分析",
            "饼图和柱状图组合/结构分析",
            data_summary
        )

        plt.close()

# 5. 现金流分析
def create_cashflow_analysis():
    """创建现金流分析图表"""
    if cashflow_df.empty:
        return

    # 按报告期汇总现金流数据
    cashflow_summary = cashflow_df.groupby(['报告期', '标准科目名称'])['金额'].sum().reset_index()
    cashflow_pivot = cashflow_summary.pivot(index='报告期', columns='标准科目名称', values='金额').fillna(0)

    if cashflow_pivot.empty:
        return

    plt.figure(figsize=(14, 10))

    # 选择主要现金流科目
    key_cashflow_items = ['经营活动现金流量净额', '投资活动现金流量净额', '筹资活动现金流量净额']
    available_items = []

    for item in key_cashflow_items:
        matching_cols = [col for col in cashflow_pivot.columns if item in col or
                        ('经营' in col and '现金' in col and item == '经营活动现金流量净额') or
                        ('投资' in col and '现金' in col and item == '投资活动现金流量净额') or
                        ('筹资' in col and '现金' in col and item == '筹资活动现金流量净额')]
        if matching_cols:
            available_items.append((item, matching_cols[0]))

    if available_items:
        fig = plt.figure(figsize=(14, 8))

        periods = cashflow_pivot.index.tolist()
        x = range(len(periods))
        width = 0.25

        colors = [COLORS['success'], COLORS['info'], COLORS['secondary']]

        for i, (label, col) in enumerate(available_items):
            values = cashflow_pivot[col].tolist()
            plt.bar([j + i * width for j in x], values, width,
                   label=label, color=colors[i % len(colors)], alpha=0.8)

        plt.title(f'{company_name} 现金流分析', fontsize=16, fontweight='bold', pad=20)
        plt.xlabel('报告期', fontsize=12)
        plt.ylabel('现金流 (元)', fontsize=12)
        plt.xticks([i + width for i in x], periods, rotation=45)
        plt.legend(fontsize=12)
        plt.grid(True, alpha=0.3, axis='y')
        plt.axhline(y=0, color='black', linestyle='-', alpha=0.5)

        plt.tight_layout()

        # 添加数据来源标注
        add_data_source_annotation(fig)

        chart_path = f'{img_dir}/06_现金流分析.png'
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')

        # 生成图表描述 - 包含实际数据信息
        data_summary = "经营、投资、筹资三大现金流的历史变化分析"

        # 添加实际数据信息
        if available_items:
            latest_values = {}
            cashflow_health = []

            for label, col in available_items:
                values = cashflow_pivot[col].tolist()
                if values:
                    latest_values[label] = values[-1]

                    # 判断现金流健康度
                    if '经营' in label:
                        cashflow_health.append(f"经营现金流{'正向' if values[-1] > 0 else '负向'}")
                    elif '投资' in label:
                        cashflow_health.append(f"投资现金流{'流出' if values[-1] < 0 else '流入'}")
                    elif '筹资' in label:
                        cashflow_health.append(f"筹资现金流{'流入' if values[-1] > 0 else '流出'}")

            # 计算现金流质量
            operating_cf = latest_values.get('经营活动现金流量净额', 0)
            investing_cf = latest_values.get('投资活动现金流量净额', 0)
            financing_cf = latest_values.get('筹资活动现金流量净额', 0)

            cf_quality = "优秀" if operating_cf > 0 and investing_cf < 0 else "良好" if operating_cf > 0 else "需关注"

            data_summary = f"""现金流分析：
            - 时间跨度：{periods[0]}至{periods[-1]}
            - 最新现金流：{', '.join([f'{k.replace("活动现金流量净额", "")}={format_number(v)}元' for k, v in latest_values.items()])}
            - 现金流特征：{', '.join(cashflow_health)}
            - 现金流质量：{cf_quality}
            - 分析要点：{'经营现金流为正，投资现金流为负，显示良好的造血能力和扩张投入' if operating_cf > 0 and investing_cf < 0 else '需关注现金流结构合理性'}"""

        chart_desc_generator.generate_description(
            "06_现金流分析",
            "柱状图/现金流分析",
            data_summary
        )

        plt.close()

# 6. 估值分析
def create_valuation_analysis():
    """创建估值分析图表"""
    if main_df.empty:
        return

    valuation_metrics = ['市盈率TTM', '市净率TTM', '市销率TTM']
    available_metrics = [metric for metric in valuation_metrics if metric in main_df.columns]

    if not available_metrics:
        return

    fig, axes = plt.subplots(1, len(available_metrics), figsize=(6*len(available_metrics), 6))
    if len(available_metrics) == 1:
        axes = [axes]

    fig.suptitle(f'{company_name} 估值指标分析', fontsize=16, fontweight='bold')

    for i, metric in enumerate(available_metrics):
        values = safe_convert_numeric(main_df[metric])
        periods = main_df['报告期']

        # 创建折线图
        axes[i].plot(periods, values, marker='o', linewidth=3, markersize=8,
                    color=COLORS['primary'])
        axes[i].fill_between(periods, values, alpha=0.3, color=COLORS['primary'])

        # 添加平均线
        avg_value = values.mean()
        axes[i].axhline(y=avg_value, color=COLORS['warning'], linestyle='--',
                       alpha=0.8, label=f'平均值: {avg_value:.1f}')

        axes[i].set_title(f'{metric}趋势', fontsize=14, fontweight='bold')
        axes[i].set_ylabel(metric, fontsize=12)
        axes[i].tick_params(axis='x', rotation=45)
        axes[i].grid(True, alpha=0.3)
        axes[i].legend()

        # 添加数值标签
        for x, y in zip(periods, values):
            if not pd.isna(y):
                axes[i].text(x, y, f'{y:.1f}', ha='center', va='bottom',
                           fontsize=10, fontweight='bold')

    plt.tight_layout()

    # 添加数据来源标注
    add_data_source_annotation(fig)

    chart_path = f'{img_dir}/07_估值分析.png'
    plt.savefig(chart_path, dpi=300, bbox_inches='tight')

    # 生成图表描述 - 包含实际估值数据
    data_summary = "PE、PB等估值指标的历史变化和合理性分析"

    # 添加实际估值数据信息
    if available_metrics:
        periods = main_df['报告期'].tolist()
        valuation_info = []

        for metric in available_metrics:
            values = safe_convert_numeric(main_df[metric])
            if len(values) > 0:
                current_value = values.iloc[-1]
                avg_value = values.mean()
                max_value = values.max()
                min_value = values.min()

                # 判断当前估值水平
                if current_value > avg_value * 1.2:
                    level = "偏高"
                elif current_value < avg_value * 0.8:
                    level = "偏低"
                else:
                    level = "合理"

                valuation_info.append(f"{metric}当前{current_value:.1f}倍({level})")

        # 计算整体估值趋势
        if len(available_metrics) > 0:
            first_metric = available_metrics[0]
            first_values = safe_convert_numeric(main_df[first_metric])
            if len(first_values) > 1:
                trend = "上升" if first_values.iloc[-1] > first_values.iloc[0] else "下降"
            else:
                trend = "稳定"

        data_summary = f"""估值分析：
        - 分析指标：{', '.join(available_metrics)}
        - 时间跨度：{periods[0]}至{periods[-1]}
        - 当前估值：{', '.join(valuation_info)}
        - 整体趋势：估值水平呈{trend}态势
        - 估值特征：{'估值回归合理区间' if '合理' in str(valuation_info) else '估值存在偏离'}
        - 分析价值：判断投资时机，识别估值修复机会，评估市场预期合理性"""

    chart_desc_generator.generate_description(
        "07_估值分析",
        "折线图/估值分析",
        data_summary
    )

    plt.close()

# 7. 综合评分雷达图
def create_comprehensive_radar():
    """创建综合评分雷达图"""
    if main_df.empty:
        return

    # 定义评分指标
    radar_metrics = {
        '盈利能力': '净利率',
        '成长能力': '营业收入同比增长率',
        '运营效率': '总资产回报率',
        '财务稳健': '资产负债率',
        '现金流': '经营现金净流量与营业收入比'
    }

    # 获取最新数据
    latest_data = main_df.iloc[0]
    scores = []
    labels = []

    for label, metric in radar_metrics.items():
        if metric in main_df.columns:
            value = safe_convert_numeric(pd.Series([latest_data[metric]])).iloc[0]
            if not pd.isna(value):
                # 简单的评分逻辑（可根据行业标准调整）
                if metric == '资产负债率':
                    score = max(0, min(100, 100 - value))  # 负债率越低越好
                elif metric in ['净利率', '总资产回报率']:
                    score = max(0, min(100, value * 10))  # 放大显示
                else:
                    score = max(0, min(100, value))

                scores.append(score)
                labels.append(label)

    if len(scores) >= 3:  # 至少需要3个指标才能画雷达图
        # 创建雷达图
        angles = np.linspace(0, 2 * np.pi, len(labels), endpoint=False).tolist()
        scores += scores[:1]  # 闭合图形
        angles += angles[:1]

        fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(projection='polar'))
        ax.plot(angles, scores, 'o-', linewidth=3, color=COLORS['primary'])
        ax.fill(angles, scores, alpha=0.25, color=COLORS['primary'])
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(labels, fontsize=12)
        ax.set_ylim(0, 100)
        ax.set_title(f'{company_name} 综合财务评分', fontsize=16, fontweight='bold', pad=30)
        ax.grid(True)

        plt.tight_layout()

        # 添加数据来源标注
        add_data_source_annotation(fig)

        chart_path = f'{img_dir}/08_综合评分雷达图.png'
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')

        # 生成图表描述 - 包含实际评分数据
        data_summary = f"综合评分雷达图：{', '.join(labels)}"

        # 添加实际评分数据信息
        if scores and labels:
            # 移除闭合点
            actual_scores = scores[:-1]

            # 计算评分统计
            avg_score = sum(actual_scores) / len(actual_scores)
            max_score = max(actual_scores)
            min_score = min(actual_scores)

            # 找到最强和最弱维度
            max_idx = actual_scores.index(max_score)
            min_idx = actual_scores.index(min_score)
            strongest_dimension = labels[max_idx]
            weakest_dimension = labels[min_idx]

            # 评分分布分析
            high_scores = [score for score in actual_scores if score >= 70]
            medium_scores = [score for score in actual_scores if 40 <= score < 70]
            low_scores = [score for score in actual_scores if score < 40]

            # 整体评价
            if avg_score >= 70:
                overall_rating = "优秀"
            elif avg_score >= 50:
                overall_rating = "良好"
            else:
                overall_rating = "需改善"

            data_summary = f"""综合评分雷达图分析：
            - 评分维度：{', '.join(labels)}
            - 平均得分：{avg_score:.1f}分
            - 最强维度：{strongest_dimension}({max_score:.1f}分)
            - 最弱维度：{weakest_dimension}({min_score:.1f}分)
            - 得分分布：优秀({len(high_scores)}项)、良好({len(medium_scores)}项)、待改善({len(low_scores)}项)
            - 整体评价：{overall_rating}
            - 分析价值：多维度评估企业综合实力，识别优势短板，指导投资决策和经营改善"""

        chart_desc_generator.generate_description(
            "08_综合评分雷达图",
            "雷达图/综合评分",
            data_summary
        )

        plt.close()

# 主函数
def generate_all_charts():
    """生成所有财务图表"""
    print(f'正在为 {company_name} ({stock_code}) 生成专业财务研报图表...')

    # 设置图表样式
    set_chart_style()

    try:
        # 1. 财务概览仪表板
        print('生成财务概览仪表板...')
        create_financial_dashboard()

        # 2. 行业对比分析
        print('生成行业对比分析...')
        create_industry_comparison()

        # 3. 财务指标趋势
        print('生成财务指标趋势...')
        create_financial_trends()

        # 4. 财务报表结构分析
        print('生成财务报表结构分析...')
        create_financial_structure_analysis()

        # 5. 现金流分析
        print('生成现金流分析...')
        create_cashflow_analysis()

        # 6. 估值分析
        print('生成估值分析...')
        create_valuation_analysis()

        # 7. 综合评分雷达图
        print('生成综合评分雷达图...')
        create_comprehensive_radar()

        # 保存图表描述到markdown文件
        print('生成图表描述文档...')
        chart_desc_generator.save_descriptions_to_markdown('chart_descriptions.md')

        print(f'\n[完成] 所有专业财务图表已生成完成！')
        print(f'[目录] 图表保存位置: {img_dir}/')
        print(f'[统计] 共生成 8 类专业财务分析图表')
        print(f'[文档] 图表描述文档: chart_descriptions.md')

    except Exception as e:
        print(f'[错误] 生成图表时出现错误: {e}')

if __name__ == '__main__':
    generate_all_charts()
