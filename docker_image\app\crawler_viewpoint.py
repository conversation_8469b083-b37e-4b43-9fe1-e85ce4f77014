import requests
from bs4 import BeautifulSoup
import pandas as pd
import time
import random
import os

headers = {
    'User-Agent': 'Mozilla/5.0'
}

base_url = 'https://guba.eastmoney.com'

def get_guba_stock_code(stock_code):
    """
    根据股票代码生成股吧URL中使用的代码格式

    Args:
        stock_code: 标准股票代码，如 '00020.HK', '06682.HK', '000001.SZ'

    Returns:
        股吧URL中使用的代码格式
    """
    if not stock_code:
        return 'hk06682'  # 默认值

    # 港股处理
    if stock_code.endswith('.HK'):
        # 去掉.HK后缀，添加hk前缀
        code_num = stock_code.replace('.HK', '')
        return f'hk{code_num}'

    # A股处理
    elif stock_code.endswith('.SZ'):
        # 深交所股票，去掉.SZ后缀，添加sz前缀
        code_num = stock_code.replace('.SZ', '')
        return f'sz{code_num}'

    elif stock_code.endswith('.SH'):
        # 上交所股票，去掉.SH后缀，添加sh前缀
        code_num = stock_code.replace('.SH', '')
        return f'sh{code_num}'

    # 美股或其他格式，暂时使用默认值
    else:
        print(f"[警告] 未知股票代码格式: {stock_code}，使用默认值")
        return 'hk06682'

# 从环境变量获取股票代码
STOCK_CODE = os.environ.get('COMPANY_CODE', '06682.HK')
COMPANY_NAME = os.environ.get('COMPANY_NAME', '4Paradigm')
GUBA_CODE = get_guba_stock_code(STOCK_CODE)

print(f"[配置] 股吧爬取配置: {COMPANY_NAME} ({STOCK_CODE}) -> 股吧代码: {GUBA_CODE}")

def get_post_list(page_url):
    """
    获取帖子列表页中的所有帖子元信息
    """
    resp = requests.get(page_url, headers=headers)
    resp.encoding = 'utf-8'
    soup = BeautifulSoup(resp.text, 'html.parser')

    posts = []
    rows = soup.select('tbody.listbody tr.listitem')

    for row in rows:
        try:
            read_count = row.select_one('.read').text.strip()
            reply_count = row.select_one('.reply').text.strip()

            title_tag = row.select_one('.title a')
            title = title_tag.text.strip()
            post_id = title_tag.get('data-postid')
            href = title_tag.get('href')
            full_url = 'https:' + href if href.startswith('//') else base_url + href

            author_tag = row.select_one('.author a')
            author_name = author_tag.text.strip() if author_tag else ''
            author_url = 'https:' + author_tag.get('href') if author_tag else ''

            time_text = row.select_one('.update').text.strip()

            posts.append({
                'title': title,
                'url': full_url,
                'post_id': post_id,
                'read_count': read_count,
                'reply_count': reply_count,
                'author': author_name,
                'author_url': author_url,
                'publish_time': time_text
            })
        except Exception as e:
            print(f"[跳过一行] 出现错误: {e}")
            continue
    return posts

def get_post_content(url):
    """
    获取帖子正文内容，包括图片链接
    """
    try:
        resp = requests.get(url, headers=headers, timeout=10)
        resp.encoding = 'utf-8'
        soup = BeautifulSoup(resp.text, 'html.parser')
        article_div = soup.find('div', class_='article-body')

        if not article_div:
            return ''

        # 获取纯文本内容
        text = article_div.get_text(separator='\n', strip=True)

        # 提取图片链接
        imgs = article_div.find_all('img')
        img_urls = [img.get('src') for img in imgs if img.get('src')]
        img_text = '\n'.join(img_urls)

        return text + ('\n\n[图片链接]\n' + img_text if img_urls else '')
    except Exception as e:
        print(f"[获取失败] {url}: {e}")
        return ''

def scrape_guba_pages(start_page=1, end_page=2):
    """
    抓取多个页面并汇总所有帖子及正文
    """
    all_data = []

    for page_num in range(start_page, end_page + 1):
        # 使用动态生成的股吧代码
        page_url = f"https://guba.eastmoney.com/list,{GUBA_CODE},99,j_{page_num}.html"
        print(f"[正在抓取列表] 第 {page_num} 页：{page_url}")
        posts = get_post_list(page_url)

        for post in posts:
            print(f"  - 正在抓取正文: {post['title']} ({post['url']})")
            content = get_post_content(post['url'])
            post['content'] = content
            time.sleep(random.uniform(5, 8.5))  # 防封 IP，适当延迟

        all_data.extend(posts)

    return all_data

if __name__ == "__main__":
    # 抓取前 3 页（你可以改成 1~N）
    scraped_posts = scrape_guba_pages(start_page=1, end_page=1)

    # 保存为 CSV
    df = pd.DataFrame(scraped_posts)
    df.to_csv("guba_viewpoint.csv", index=False, encoding='utf-8-sig')
    print("[完成] 已保存为 CSV 文件。")
